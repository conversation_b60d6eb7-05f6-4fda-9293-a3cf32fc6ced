@echo off
REM Biomedical Electronics Virtual Lab - Windows Deployment Script
REM This script prepares files for deployment to web hosting

echo.
echo 🚀 Biomedical Virtual Lab Deployment Preparation
echo ================================================

REM Create deployment directory
set DEPLOY_DIR=deployment
echo 📁 Creating deployment directory...
if exist %DEPLOY_DIR% rmdir /s /q %DEPLOY_DIR%
mkdir %DEPLOY_DIR%
mkdir %DEPLOY_DIR%\assets
mkdir %DEPLOY_DIR%\assets\css
mkdir %DEPLOY_DIR%\assets\js
mkdir %DEPLOY_DIR%\assets\images
mkdir %DEPLOY_DIR%\assets\images\screenshots
mkdir %DEPLOY_DIR%\assets\videos

REM Copy and rename main files
echo 📄 Copying main HTML files...
copy landing-page.html %DEPLOY_DIR%\index.html >nul
copy curriculum-details.html %DEPLOY_DIR%\curriculum.html >nul
copy pricing.html %DEPLOY_DIR%\pricing.html >nul
copy 404.html %DEPLOY_DIR%\404.html >nul

REM Copy CSS and JS files
echo 🎨 Copying stylesheets and scripts...
copy landing-styles.css %DEPLOY_DIR%\assets\css\styles.css >nul
copy landing-script.js %DEPLOY_DIR%\assets\js\script.js >nul

REM Copy configuration files
echo ⚙️ Copying configuration files...
copy .htaccess %DEPLOY_DIR%\.htaccess >nul
copy robots.txt %DEPLOY_DIR%\robots.txt >nul
copy sitemap.xml %DEPLOY_DIR%\sitemap.xml >nul

REM Update file paths in HTML files using PowerShell
echo 🔗 Updating asset paths in HTML files...
powershell -Command "(Get-Content '%DEPLOY_DIR%\index.html') -replace 'landing-styles\.css', 'assets/css/styles.css' | Set-Content '%DEPLOY_DIR%\index.html'"
powershell -Command "(Get-Content '%DEPLOY_DIR%\index.html') -replace 'landing-script\.js', 'assets/js/script.js' | Set-Content '%DEPLOY_DIR%\index.html'"

powershell -Command "(Get-Content '%DEPLOY_DIR%\curriculum.html') -replace 'landing-styles\.css', 'assets/css/styles.css' | Set-Content '%DEPLOY_DIR%\curriculum.html'"
powershell -Command "(Get-Content '%DEPLOY_DIR%\curriculum.html') -replace 'landing-script\.js', 'assets/js/script.js' | Set-Content '%DEPLOY_DIR%\curriculum.html'"

powershell -Command "(Get-Content '%DEPLOY_DIR%\pricing.html') -replace 'landing-styles\.css', 'assets/css/styles.css' | Set-Content '%DEPLOY_DIR%\pricing.html'"
powershell -Command "(Get-Content '%DEPLOY_DIR%\pricing.html') -replace 'landing-script\.js', 'assets/js/script.js' | Set-Content '%DEPLOY_DIR%\pricing.html'"

REM Create placeholder files
echo 🖼️ Creating placeholder image structure...
echo. > %DEPLOY_DIR%\assets\images\logo.png
echo. > %DEPLOY_DIR%\assets\images\hero-bg.jpg
echo. > %DEPLOY_DIR%\assets\images\screenshots\breadboard.png
echo. > %DEPLOY_DIR%\assets\images\screenshots\oscilloscope.png
echo. > %DEPLOY_DIR%\assets\images\screenshots\dashboard.png
echo. > %DEPLOY_DIR%\assets\images\screenshots\fault-diagnosis.png

echo 🎥 Creating placeholder video structure...
echo. > %DEPLOY_DIR%\assets\videos\hero-demo.mp4

REM Create README for deployment
echo 📋 Creating deployment documentation...
(
echo # Biomedical Electronics Virtual Lab - Deployment Files
echo.
echo ## Files Ready for Upload
echo.
echo ### Main Files
echo - `index.html` - Main landing page
echo - `curriculum.html` - Detailed curriculum page  
echo - `pricing.html` - Pricing and plans page
echo - `404.html` - Custom error page
echo.
echo ### Assets
echo - `assets/css/styles.css` - Main stylesheet
echo - `assets/js/script.js` - Interactive functionality
echo - `assets/images/` - Image assets ^(placeholders created^)
echo - `assets/videos/` - Video assets ^(placeholders created^)
echo.
echo ### Configuration
echo - `.htaccess` - Apache server configuration
echo - `robots.txt` - Search engine crawler instructions
echo - `sitemap.xml` - Site structure for search engines
echo.
echo ## Deployment Instructions
echo.
echo 1. **Upload all files** to your web server's public directory
echo 2. **Configure your domain** to point to the hosting server
echo 3. **Verify SSL certificate** is installed and working
echo 4. **Test all pages** and functionality
echo 5. **Submit sitemap** to Google Search Console
echo.
echo ## Next Steps
echo.
echo 1. Replace placeholder images with actual screenshots
echo 2. Add the hero video ^(hero-demo.mp4^)
echo 3. Set up Google Analytics tracking
echo 4. Configure email automation
echo 5. Begin A/B testing
echo.
echo ## Support
echo.
echo For technical support, contact:
echo Dr. Mohammed Yagoub Esmail
echo Email: <EMAIL>
echo Phone: +249 912 867 327 ^| +966 538 076 790
) > %DEPLOY_DIR%\README.md

REM Create deployment checklist
(
echo # Deployment Checklist
echo.
echo ## Pre-Deployment
echo - [ ] Domain purchased and configured
echo - [ ] Hosting account set up
echo - [ ] SSL certificate ready
echo - [ ] Backup of existing site ^(if applicable^)
echo.
echo ## File Upload
echo - [ ] Upload all HTML files
echo - [ ] Upload assets folder with CSS/JS
echo - [ ] Upload configuration files ^(.htaccess, robots.txt, sitemap.xml^)
echo - [ ] Set proper file permissions ^(644 for files, 755 for directories^)
echo.
echo ## Testing
echo - [ ] All pages load correctly
echo - [ ] Navigation works on desktop and mobile
echo - [ ] Forms function properly ^(when implemented^)
echo - [ ] SSL certificate is active ^(https://^)
echo - [ ] 404 page displays correctly
echo - [ ] Site loads in under 3 seconds
echo.
echo ## SEO Setup
echo - [ ] Submit sitemap to Google Search Console
echo - [ ] Submit sitemap to Bing Webmaster Tools
echo - [ ] Verify meta tags are correct
echo - [ ] Check robots.txt is accessible
echo.
echo ## Performance
echo - [ ] Run Google PageSpeed Insights test
echo - [ ] Check GTmetrix performance score
echo - [ ] Verify Core Web Vitals are in "Good" range
echo - [ ] Test on multiple devices and browsers
echo.
echo ## Security
echo - [ ] HTTPS redirect working
echo - [ ] Security headers configured
echo - [ ] Sensitive files protected
echo - [ ] Regular backup schedule established
echo.
echo ## Analytics ^(Next Phase^)
echo - [ ] Google Analytics 4 installed
echo - [ ] Google Tag Manager configured
echo - [ ] Conversion tracking set up
echo - [ ] Email automation connected
) > %DEPLOY_DIR%\deployment-checklist.md

REM Count files
for /f %%i in ('dir /s /b %DEPLOY_DIR% ^| find /c /v ""') do set FILE_COUNT=%%i

echo.
echo ✅ Deployment preparation complete!
echo 📊 Summary:
echo    - Total files: %FILE_COUNT%
echo    - Location: .\%DEPLOY_DIR%\
echo.
echo 📁 Files ready for upload:
echo    - Main pages: index.html, curriculum.html, pricing.html
echo    - Assets: CSS, JS, images ^(placeholders^), videos ^(placeholders^)
echo    - Config: .htaccess, robots.txt, sitemap.xml
echo.
echo 🌐 Next steps:
echo    1. Upload contents of '%DEPLOY_DIR%' to your web server
echo    2. Configure your domain DNS settings
echo    3. Verify SSL certificate installation
echo    4. Test all functionality
echo    5. Submit sitemap to search engines
echo.
echo 📞 Need help? Contact Dr. Mohammed Yagoub Esmail
echo    Email: <EMAIL>
echo    Phone: +249 912 867 327 ^| +966 538 076 790
echo.
echo 🎯 Ready to deploy your Biomedical Electronics Virtual Lab!
echo.
pause
