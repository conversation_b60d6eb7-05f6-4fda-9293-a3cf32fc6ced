import React, { useState } from 'react';
import type { DiagramInfo } from '../constants';

interface DiagramGalleryProps {
  diagrams: DiagramInfo[];
}

export const DiagramGallery: React.FC<DiagramGalleryProps> = ({ diagrams }) => {
  const [currentIndex, setCurrentIndex] = useState(0);

  if (!diagrams || diagrams.length === 0) {
    return (
      <div className="bg-white p-4 rounded-lg shadow-lg text-center">
        <p className="text-slate-500">No diagrams available.</p>
      </div>
    );
  }

  const currentDiagram = diagrams[currentIndex];

  const goToPrevious = () => {
    setCurrentIndex((prevIndex) =>
      prevIndex === 0 ? diagrams.length - 1 : prevIndex - 1
    );
  };

  const goToNext = () => {
    setCurrentIndex((prevIndex) =>
      prevIndex === diagrams.length - 1 ? 0 : prevIndex + 1
    );
  };

  return (
    <div className="bg-white p-4 rounded-lg shadow-lg">
      <h3 className="text-md font-semibold text-blue-700 mb-2 text-center truncate" title={currentDiagram.title}>
        {currentDiagram.title}
      </h3>
      <div className="relative mb-3">
        <img
          src={currentDiagram.imageUrl}
          alt={currentDiagram.title}
          className="w-full h-auto object-contain rounded-md max-h-60 border border-slate-200"
          loading="lazy"
        />
      </div>
      <p className="text-xs text-slate-600 mb-3 h-16 overflow-y-auto custom-scrollbar">
        {currentDiagram.description}
      </p>
      {diagrams.length > 1 && (
        <div className="flex justify-between items-center">
          <button
            onClick={goToPrevious}
            aria-label="Previous diagram"
            className="px-3 py-1 text-xs bg-blue-500 hover:bg-blue-600 text-white rounded-md shadow-sm transition-colors duration-150 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
          >
            &larr; Prev
          </button>
          <span className="text-xs text-slate-500">
            {currentIndex + 1} / {diagrams.length}
          </span>
          <button
            onClick={goToNext}
            aria-label="Next diagram"
            className="px-3 py-1 text-xs bg-blue-500 hover:bg-blue-600 text-white rounded-md shadow-sm transition-colors duration-150 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
          >
            Next &rarr;
          </button>
        </div>
      )}
       <style>{`
        .custom-scrollbar::-webkit-scrollbar {
          width: 6px;
        }
        .custom-scrollbar::-webkit-scrollbar-track {
          background: #f1f5f9; // slate-100
          border-radius: 10px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb {
          background: #94a3b8; // slate-400
          border-radius: 10px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
          background: #64748b; // slate-500
        }
      `}</style>
    </div>
  );
};
