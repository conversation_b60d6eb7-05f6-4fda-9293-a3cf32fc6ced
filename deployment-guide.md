# Deployment Guide for Biomedical Electronics Virtual Lab

## 🚀 Quick Deployment Options

### Option 1: Netlify (Recommended for Quick Setup)
**Pros:** Free tier, automatic SSL, easy domain setup, Git integration
**Time to Deploy:** 5-10 minutes

1. **Create Netlify Account:** Go to [netlify.com](https://netlify.com) and sign up
2. **Drag & Drop Deployment:**
   - Zip all files: `landing-page.html`, `landing-styles.css`, `landing-script.js`, `curriculum-details.html`, `pricing.html`
   - Drag the zip file to Netlify's deploy area
   - Get instant URL: `https://random-name.netlify.app`

3. **Custom Domain Setup:**
   - In Netlify dashboard: Site Settings → Domain Management
   - Add custom domain: `biomedvirtuallab.com` (or your chosen domain)
   - Follow DNS configuration instructions

### Option 2: Vercel (Great for React/Next.js Future)
**Pros:** Excellent performance, free tier, automatic deployments
**Time to Deploy:** 5-10 minutes

1. **Create Vercel Account:** Go to [vercel.com](https://vercel.com)
2. **Deploy Static Files:**
   - Create new project
   - Upload files or connect Git repository
   - Automatic deployment with SSL

### Option 3: Traditional Web Hosting (cPanel/Shared Hosting)
**Pros:** Full control, can use existing hosting
**Time to Deploy:** 15-30 minutes

## 📁 File Structure for Deployment

```
/public_html (or root directory)
├── index.html (rename landing-page.html)
├── curriculum.html (rename curriculum-details.html)
├── pricing.html
├── assets/
│   ├── css/
│   │   └── styles.css (rename landing-styles.css)
│   ├── js/
│   │   └── script.js (rename landing-script.js)
│   ├── images/
│   │   ├── logo.png
│   │   ├── hero-bg.jpg
│   │   └── screenshots/
│   └── videos/
│       └── hero-demo.mp4
├── .htaccess (for Apache servers)
├── robots.txt
└── sitemap.xml
```

## 🔧 Pre-Deployment Checklist

### 1. Update File Paths
Update the HTML files to use the new asset structure:

```html
<!-- In index.html, curriculum.html, pricing.html -->
<link rel="stylesheet" href="assets/css/styles.css">
<script src="assets/js/script.js"></script>
```

### 2. Create .htaccess File (for Apache)
```apache
# Force HTTPS
RewriteEngine On
RewriteCond %{HTTPS} off
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Compress files
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Cache static files
<IfModule mod_expires.c>
    ExpiresActive on
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
</IfModule>

# Custom error pages
ErrorDocument 404 /404.html
```

### 3. Create robots.txt
```
User-agent: *
Allow: /

Sitemap: https://yourdomain.com/sitemap.xml
```

### 4. Create Basic sitemap.xml
```xml
<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <url>
    <loc>https://yourdomain.com/</loc>
    <lastmod>2025-01-10</lastmod>
    <priority>1.0</priority>
  </url>
  <url>
    <loc>https://yourdomain.com/curriculum.html</loc>
    <lastmod>2025-01-10</lastmod>
    <priority>0.8</priority>
  </url>
  <url>
    <loc>https://yourdomain.com/pricing.html</loc>
    <lastmod>2025-01-10</lastmod>
    <priority>0.8</priority>
  </url>
</urlset>
```

## 🌐 Domain Configuration

### DNS Settings (Point to Hosting Provider)
```
Type    Name    Value                   TTL
A       @       [Your hosting IP]       3600
A       www     [Your hosting IP]       3600
CNAME   www     yourdomain.com          3600
```

### Recommended Domain Names
- `biomedvirtuallab.com`
- `biomedicalelectronicslab.com`
- `virtualbiomediclab.com`
- `biomedcircuitlab.com`

## 🔒 SSL Certificate Setup

### Automatic SSL (Recommended)
Most modern hosting providers offer free SSL:
- **Netlify/Vercel:** Automatic Let's Encrypt SSL
- **Cloudflare:** Free SSL with CDN benefits
- **cPanel Hosting:** Usually includes AutoSSL

### Manual SSL Setup
If manual setup is required:
1. Purchase SSL certificate or use Let's Encrypt
2. Generate CSR (Certificate Signing Request)
3. Install certificate through hosting control panel
4. Update .htaccess to force HTTPS

## 📊 Performance Optimization

### Image Optimization
```bash
# Install imagemin for optimization
npm install -g imagemin-cli imagemin-webp imagemin-mozjpeg

# Optimize images
imagemin assets/images/*.jpg --out-dir=assets/images/optimized --plugin=mozjpeg
imagemin assets/images/*.png --out-dir=assets/images/optimized --plugin=pngquant
```

### CDN Setup (Optional but Recommended)
1. **Cloudflare (Free):**
   - Sign up at cloudflare.com
   - Add your domain
   - Update nameservers
   - Enable caching and minification

2. **Benefits:**
   - Faster global loading times
   - DDoS protection
   - Additional SSL options
   - Bandwidth savings

## 🧪 Testing After Deployment

### 1. Functionality Testing
- [ ] All pages load correctly
- [ ] Navigation works on all devices
- [ ] Forms submit properly (when implemented)
- [ ] Mobile responsiveness
- [ ] Cross-browser compatibility

### 2. Performance Testing
- [ ] Google PageSpeed Insights score >90
- [ ] GTmetrix performance grade A
- [ ] Load time <3 seconds on 3G
- [ ] Core Web Vitals in "Good" range

### 3. SEO Testing
- [ ] Meta tags present and correct
- [ ] Structured data validates
- [ ] Sitemap accessible
- [ ] robots.txt configured
- [ ] SSL certificate active

## 🔧 Deployment Scripts

### Simple Deployment Script (deploy.sh)
```bash
#!/bin/bash

# Biomedical Virtual Lab Deployment Script

echo "🚀 Starting deployment..."

# Create directory structure
mkdir -p assets/{css,js,images,videos}

# Copy and rename files
cp landing-page.html index.html
cp curriculum-details.html curriculum.html
cp landing-styles.css assets/css/styles.css
cp landing-script.js assets/js/script.js

# Update file paths in HTML
sed -i 's/landing-styles.css/assets\/css\/styles.css/g' *.html
sed -i 's/landing-script.js/assets\/js\/script.js/g' *.html

echo "✅ Files prepared for deployment"
echo "📁 Upload the following to your web server:"
echo "   - index.html"
echo "   - curriculum.html" 
echo "   - pricing.html"
echo "   - assets/ folder"
echo "   - .htaccess"
echo "   - robots.txt"
echo "   - sitemap.xml"

echo "🌐 Don't forget to:"
echo "   1. Configure your domain DNS"
echo "   2. Set up SSL certificate"
echo "   3. Test all functionality"
echo "   4. Submit sitemap to Google Search Console"
```

## 📞 Support & Troubleshooting

### Common Issues

**Issue:** CSS/JS files not loading
**Solution:** Check file paths and case sensitivity

**Issue:** SSL certificate errors
**Solution:** Verify certificate installation and force HTTPS redirect

**Issue:** Slow loading times
**Solution:** Enable compression, optimize images, use CDN

**Issue:** Mobile display problems
**Solution:** Test viewport meta tag and responsive CSS

### Getting Help
- **Hosting Support:** Contact your hosting provider's technical support
- **DNS Issues:** Use tools like `dig` or online DNS checkers
- **SSL Problems:** Use SSL checker tools online
- **Performance:** Use Google PageSpeed Insights for specific recommendations

## 🎯 Post-Deployment Actions

1. **Submit to Search Engines:**
   - Google Search Console
   - Bing Webmaster Tools
   - Submit sitemap

2. **Set Up Monitoring:**
   - Uptime monitoring (UptimeRobot, Pingdom)
   - Performance monitoring
   - Error tracking

3. **Backup Strategy:**
   - Regular file backups
   - Database backups (if applicable)
   - Version control with Git

4. **Security Measures:**
   - Regular updates
   - Security headers
   - Firewall configuration
   - Regular security scans

---

**Next Step:** Once deployment is complete, proceed to analytics setup for tracking user behavior and conversions.
