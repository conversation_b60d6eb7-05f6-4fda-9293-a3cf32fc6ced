
import { GoogleGenAI } from "@google/genai";
import type { GeminiVitalSignsResponse, ECGPoint } from '../types';
import { INITIAL_ECG_DATA } from '../constants';

// Ensure API_KEY is set in environment variables
const apiKey = process.env.API_KEY;
if (!apiKey) {
  throw new Error("API_KEY for Gemini is not set in environment variables.");
}
const ai = new GoogleGenAI({ apiKey });

const generateDefaultECG = (pattern: string, points = 100): ECGPoint[] => {
  const waveform: ECGPoint[] = [];
  // Simple placeholder logic, can be expanded
  for (let i = 0; i < points; i++) {
    let value = Math.sin(i * 0.2) * 0.5; // Basic sine wave
    if (pattern.toLowerCase().includes("tachycardia")) {
      value = Math.sin(i * 0.4) * 0.6; // Faster
    } else if (pattern.toLowerCase().includes("bradycardia")) {
      value = Math.sin(i * 0.1) * 0.4; // Slower
    } else if (pattern.toLowerCase().includes("fibrillation") || pattern.toLowerCase().includes("afib")) {
      value = (Math.random() - 0.5) * 0.8; // Irregular
    } else if (pattern.toLowerCase().includes("flat line")) {
        value = 0.05; // Almost flat
    }
    // Add some PQRST like features for normal
    if (pattern.toLowerCase().includes("normal") || !pattern) {
        if (i % 50 < 5) value = 0.2 + Math.random()*0.1; // P wave
        else if (i % 50 < 7) value = -0.1; // Q
        else if (i % 50 < 12) value = 1.0 + Math.random()*0.2; // R peak
        else if (i % 50 < 15) value = -0.3; // S
        else if (i % 50 < 25) value = 0.4 + Math.random()*0.1; // T wave
        else value = 0; // Isoelectric line
    }

    waveform.push({ time: i * 10, value: parseFloat(value.toFixed(2)) }); // time in ms like
  }
  return waveform;
};


export const fetchVitalSignsData = async (scenario: string): Promise<GeminiVitalSignsResponse> => {
  const model = 'gemini-2.5-flash-preview-04-17';
  
  const prompt = `
    Given a patient scenario: "${scenario}".
    Provide typical vital signs and a simplified ECG waveform data.
    Respond ONLY in JSON format with no additional text or markdown.
    The JSON object should have the following structure:
    {
      "heartRate": number, // (bpm), e.g., 70
      "spo2": number, // (%), e.g., 98
      "systolicBP": number, // (mmHg), e.g., 120
      "diastolicBP": number, // (mmHg), e.g., 80
      "respiratoryRate": number, // (breaths/min), e.g., 16
      "temperature": number, // (°C), e.g., 37.0
      "ecgDescription": "string describing the ECG pattern", // e.g., "Normal sinus rhythm"
      "ecgWaveform": [ // Array of 50-100 points representing one or two cardiac cycles. Time is arbitrary sequence. Value between -1.5 and 2.0.
        { "time": number, "value": number },
        // ... more points
      ]
    }

    Example for "Normal Healthy Adult":
    {
      "heartRate": 75,
      "spo2": 98,
      "systolicBP": 120,
      "diastolicBP": 80,
      "respiratoryRate": 16,
      "temperature": 37.0,
      "ecgDescription": "Normal sinus rhythm with regular P, QRS, and T waves.",
      "ecgWaveform": [
        {"time": 0, "value": 0.1}, {"time": 10, "value": 0.2}, {"time": 20, "value": 0.0}, {"time": 30, "value": -0.1},
        {"time": 40, "value": 1.0}, {"time": 50, "value": -0.3}, {"time": 60, "value": 0.0}, {"time": 70, "value": 0.3},
        {"time": 80, "value": 0.4}, {"time": 90, "value": 0.1}, {"time": 100, "value": 0.0}
      ]
    }

    Ensure ecgWaveform data points are plausible for the scenario. For example:
    - For 'Tachycardia': Faster PQRST complexes.
    - For 'Bradycardia': Slower PQRST complexes.
    - For 'Atrial Fibrillation': Irregular rhythm, often no clear P waves, variable R-R intervals.
    - For 'Fever': May show sinus tachycardia.
    - For 'Hypothermia': May show bradycardia, J waves (Osborn waves).
  `;

  try {
    const response = await ai.models.generateContent({
      model: model,
      contents: prompt,
      config: {
        responseMimeType: "application/json",
        temperature: 0.5, // Adjust for creativity vs. predictability
      }
    });

    let jsonStr = response.text.trim();
    // Remove markdown fences if present
    const fenceRegex = /^```(\w*)?\s*\n?(.*?)\n?\s*```$/s;
    const match = jsonStr.match(fenceRegex);
    if (match && match[2]) {
      jsonStr = match[2].trim();
    }
    
    const parsedData = JSON.parse(jsonStr) as GeminiVitalSignsResponse;

    // Validate and supplement ECG data if necessary
    if (!parsedData.ecgWaveform || parsedData.ecgWaveform.length === 0) {
      console.warn("Gemini did not return ECG waveform data, generating default.");
      parsedData.ecgWaveform = generateDefaultECG(parsedData.ecgDescription || scenario);
    } else {
      // Ensure values are numbers
      parsedData.ecgWaveform = parsedData.ecgWaveform.map(pt => ({
        time: Number(pt.time) || 0,
        value: Number(pt.value) || 0
      }));
    }
    
    // Basic validation for other fields
    parsedData.heartRate = Number(parsedData.heartRate) || 70;
    parsedData.spo2 = Number(parsedData.spo2) || 98;
    parsedData.systolicBP = Number(parsedData.systolicBP) || 120;
    parsedData.diastolicBP = Number(parsedData.diastolicBP) || 80;
    parsedData.respiratoryRate = Number(parsedData.respiratoryRate) || 16;
    parsedData.temperature = Number(parsedData.temperature) || 37.0;
    parsedData.ecgDescription = parsedData.ecgDescription || "ECG data generated.";


    return parsedData;

  } catch (error) {
    console.error("Error calling Gemini API or parsing response:", error);
    // Fallback to a default error state or rethrow
    // For now, let's provide some default data for a known scenario on error
    if (scenario === "Normal Healthy Adult") {
        return {
            heartRate: 72, spo2: 98, systolicBP: 118, diastolicBP: 78, respiratoryRate: 15, temperature: 36.8,
            ecgDescription: "Default Normal Sinus Rhythm (fallback)",
            ecgWaveform: generateDefaultECG("Normal Healthy Adult")
        };
    }
    // More generic fallback
    return {
        heartRate: 0, spo2: 0, systolicBP: 0, diastolicBP: 0, respiratoryRate: 0, temperature: 0,
        ecgDescription: "Error fetching data. Displaying placeholder.",
        ecgWaveform: INITIAL_ECG_DATA
    };
  }
};
