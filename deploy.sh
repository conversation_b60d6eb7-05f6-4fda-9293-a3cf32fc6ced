#!/bin/bash

# Biomedical Electronics Virtual Lab - Deployment Script
# This script prepares files for deployment to web hosting

echo "🚀 Biomedical Virtual Lab Deployment Preparation"
echo "================================================"

# Create deployment directory
DEPLOY_DIR="deployment"
echo "📁 Creating deployment directory..."
mkdir -p $DEPLOY_DIR
mkdir -p $DEPLOY_DIR/assets/{css,js,images,videos}

# Copy and rename main files
echo "📄 Copying main HTML files..."
cp landing-page.html $DEPLOY_DIR/index.html
cp curriculum-details.html $DEPLOY_DIR/curriculum.html
cp pricing.html $DEPLOY_DIR/pricing.html
cp 404.html $DEPLOY_DIR/404.html

# Copy CSS and JS files
echo "🎨 Copying stylesheets and scripts..."
cp landing-styles.css $DEPLOY_DIR/assets/css/styles.css
cp landing-script.js $DEPLOY_DIR/assets/js/script.js

# Copy configuration files
echo "⚙️ Copying configuration files..."
cp .htaccess $DEPLOY_DIR/.htaccess
cp robots.txt $DEPLOY_DIR/robots.txt
cp sitemap.xml $DEPLOY_DIR/sitemap.xml

# Update file paths in HTML files
echo "🔗 Updating asset paths in HTML files..."
sed -i.bak 's/landing-styles\.css/assets\/css\/styles.css/g' $DEPLOY_DIR/*.html
sed -i.bak 's/landing-script\.js/assets\/js\/script.js/g' $DEPLOY_DIR/*.html

# Remove backup files created by sed
rm $DEPLOY_DIR/*.html.bak

# Create placeholder image files
echo "🖼️ Creating placeholder image structure..."
touch $DEPLOY_DIR/assets/images/logo.png
touch $DEPLOY_DIR/assets/images/hero-bg.jpg
mkdir -p $DEPLOY_DIR/assets/images/screenshots
touch $DEPLOY_DIR/assets/images/screenshots/breadboard.png
touch $DEPLOY_DIR/assets/images/screenshots/oscilloscope.png
touch $DEPLOY_DIR/assets/images/screenshots/dashboard.png
touch $DEPLOY_DIR/assets/images/screenshots/fault-diagnosis.png

# Create placeholder video files
echo "🎥 Creating placeholder video structure..."
touch $DEPLOY_DIR/assets/videos/hero-demo.mp4

# Create README for deployment
cat > $DEPLOY_DIR/README.md << 'EOF'
# Biomedical Electronics Virtual Lab - Deployment Files

## Files Ready for Upload

### Main Files
- `index.html` - Main landing page
- `curriculum.html` - Detailed curriculum page  
- `pricing.html` - Pricing and plans page
- `404.html` - Custom error page

### Assets
- `assets/css/styles.css` - Main stylesheet
- `assets/js/script.js` - Interactive functionality
- `assets/images/` - Image assets (placeholders created)
- `assets/videos/` - Video assets (placeholders created)

### Configuration
- `.htaccess` - Apache server configuration
- `robots.txt` - Search engine crawler instructions
- `sitemap.xml` - Site structure for search engines

## Deployment Instructions

1. **Upload all files** to your web server's public directory
2. **Configure your domain** to point to the hosting server
3. **Verify SSL certificate** is installed and working
4. **Test all pages** and functionality
5. **Submit sitemap** to Google Search Console

## Next Steps

1. Replace placeholder images with actual screenshots
2. Add the hero video (hero-demo.mp4)
3. Set up Google Analytics tracking
4. Configure email automation
5. Begin A/B testing

## Support

For technical support, contact:
Dr. Mohammed Yagoub Esmail
Email: <EMAIL>
Phone: +249 912 867 327 | +966 538 076 790
EOF

# Create deployment checklist
cat > $DEPLOY_DIR/deployment-checklist.md << 'EOF'
# Deployment Checklist

## Pre-Deployment
- [ ] Domain purchased and configured
- [ ] Hosting account set up
- [ ] SSL certificate ready
- [ ] Backup of existing site (if applicable)

## File Upload
- [ ] Upload all HTML files
- [ ] Upload assets folder with CSS/JS
- [ ] Upload configuration files (.htaccess, robots.txt, sitemap.xml)
- [ ] Set proper file permissions (644 for files, 755 for directories)

## Testing
- [ ] All pages load correctly
- [ ] Navigation works on desktop and mobile
- [ ] Forms function properly (when implemented)
- [ ] SSL certificate is active (https://)
- [ ] 404 page displays correctly
- [ ] Site loads in under 3 seconds

## SEO Setup
- [ ] Submit sitemap to Google Search Console
- [ ] Submit sitemap to Bing Webmaster Tools
- [ ] Verify meta tags are correct
- [ ] Check robots.txt is accessible

## Performance
- [ ] Run Google PageSpeed Insights test
- [ ] Check GTmetrix performance score
- [ ] Verify Core Web Vitals are in "Good" range
- [ ] Test on multiple devices and browsers

## Security
- [ ] HTTPS redirect working
- [ ] Security headers configured
- [ ] Sensitive files protected
- [ ] Regular backup schedule established

## Analytics (Next Phase)
- [ ] Google Analytics 4 installed
- [ ] Google Tag Manager configured
- [ ] Conversion tracking set up
- [ ] Email automation connected
EOF

# Create a simple file list
echo "📋 Creating file inventory..."
find $DEPLOY_DIR -type f | sort > $DEPLOY_DIR/file-list.txt

# Calculate total size
TOTAL_SIZE=$(du -sh $DEPLOY_DIR | cut -f1)

echo ""
echo "✅ Deployment preparation complete!"
echo "📊 Summary:"
echo "   - Total files: $(find $DEPLOY_DIR -type f | wc -l)"
echo "   - Total size: $TOTAL_SIZE"
echo "   - Location: ./$DEPLOY_DIR/"
echo ""
echo "📁 Files ready for upload:"
echo "   - Main pages: index.html, curriculum.html, pricing.html"
echo "   - Assets: CSS, JS, images (placeholders), videos (placeholders)"
echo "   - Config: .htaccess, robots.txt, sitemap.xml"
echo ""
echo "🌐 Next steps:"
echo "   1. Upload contents of '$DEPLOY_DIR' to your web server"
echo "   2. Configure your domain DNS settings"
echo "   3. Verify SSL certificate installation"
echo "   4. Test all functionality"
echo "   5. Submit sitemap to search engines"
echo ""
echo "📞 Need help? Contact Dr. Mohammed Yagoub Esmail"
echo "   Email: <EMAIL>"
echo "   Phone: +249 912 867 327 | +966 538 076 790"
echo ""
echo "🎯 Ready to deploy your Biomedical Electronics Virtual Lab!"
