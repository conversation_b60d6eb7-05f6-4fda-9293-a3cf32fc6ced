
import React, { useState } from 'react';
import type { ECGSystemComponentDetail } from '../types';

interface InteractiveDiagramsPageProps {
  components: ECGSystemComponentDetail[];
}

export const InteractiveDiagramsPage: React.FC<InteractiveDiagramsPageProps> = ({ components }) => {
  const [selectedComponentId, setSelectedComponentId] = useState<string | null>(components.length > 0 ? components[0].id : null);

  const selectedComponent = components.find(comp => comp.id === selectedComponentId);

  if (components.length === 0) {
    return <div className="w-full text-center p-10 text-slate-600">No diagram components available.</div>;
  }

  return (
    <div className="w-full flex flex-col md:flex-row gap-6">
      {/* Sidebar for component selection */}
      <aside className="w-full md:w-1/4 lg:w-1/5 bg-white p-4 rounded-lg shadow-lg self-start">
        <h2 className="text-xl font-semibold text-blue-700 mb-4 border-b pb-2">ECG System Components</h2>
        <nav>
          <ul className="space-y-1">
            {components.map(comp => (
              <li key={comp.id}>
                <button
                  onClick={() => setSelectedComponentId(comp.id)}
                  className={`w-full text-left px-3 py-2 rounded-md text-sm font-medium transition-colors duration-150
                    ${selectedComponentId === comp.id 
                      ? 'bg-blue-600 text-white shadow-md' 
                      : 'bg-slate-50 hover:bg-blue-100 text-slate-700 hover:text-blue-700'
                    }`}
                  aria-current={selectedComponentId === comp.id ? 'page' : undefined}
                >
                  {comp.title}
                  <span className="block text-xs opacity-70">{comp.type}</span>
                </button>
              </li>
            ))}
          </ul>
        </nav>
      </aside>

      {/* Main content area for displaying selected component details */}
      <section className="w-full md:w-3/4 lg:w-4/5 bg-white p-6 rounded-lg shadow-lg">
        {selectedComponent ? (
          <article className="space-y-6">
            <header>
              <h1 className="text-2xl md:text-3xl font-bold text-blue-800 mb-1">{selectedComponent.title}</h1>
              <p className="text-sm text-slate-500 italic">Type: {selectedComponent.type}</p>
            </header>

            <figure className="mb-4 p-2 border border-slate-200 rounded-md bg-slate-50">
              <img 
                src={selectedComponent.imageUrl} 
                alt={selectedComponent.title} 
                className="w-full h-auto max-h-[400px] md:max-h-[500px] object-contain rounded" 
                loading="lazy"
              />
            </figure>

            <div>
              <h2 className="text-xl font-semibold text-blue-700 mb-2 border-b pb-1">Overview</h2>
              <p className="text-slate-700 leading-relaxed">{selectedComponent.overview}</p>
            </div>

            <div>
              <h2 className="text-xl font-semibold text-blue-700 mb-3 border-b pb-1">Key Aspects</h2>
              <ul className="space-y-3">
                {selectedComponent.keyAspects.map((aspect, index) => (
                  <li key={index} className="p-3 bg-slate-50 rounded-md shadow-sm">
                    <h3 className="font-semibold text-slate-800">{aspect.name}</h3>
                    <p className="text-sm text-slate-600 mt-1">{aspect.description}</p>
                    {aspect.highlightPoints && aspect.highlightPoints.length > 0 && (
                      <ul className="list-disc list-inside mt-2 space-y-1 pl-4">
                        {aspect.highlightPoints.map((point, pIndex) => (
                          <li key={pIndex} className="text-xs text-slate-500">{point}</li>
                        ))}
                      </ul>
                    )}
                  </li>
                ))}
              </ul>
            </div>

            {selectedComponent.signalPath && selectedComponent.signalPath.length > 0 && (
              <div>
                <h2 className="text-xl font-semibold text-blue-700 mb-3 border-b pb-1">Signal Path / Process Flow</h2>
                <ol className="space-y-3">
                  {selectedComponent.signalPath.map((step, index) => (
                    <li key={index} className="flex items-start p-3 bg-slate-50 rounded-md shadow-sm">
                      <span className="mr-3 mt-1 flex-shrink-0 bg-blue-600 text-white w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold">
                        {index + 1}
                      </span>
                      <div>
                        <h3 className="font-semibold text-slate-800">{step.stage}</h3>
                        <p className="text-sm text-slate-600 mt-1">{step.description}</p>
                        {step.details && step.details.length > 0 && (
                           <ul className="list-disc list-inside mt-2 space-y-1 pl-4">
                            {step.details.map((detail, dIndex) => (
                              <li key={dIndex} className="text-xs text-slate-500">{detail}</li>
                            ))}
                          </ul>
                        )}
                      </div>
                    </li>
                  ))}
                </ol>
              </div>
            )}
          </article>
        ) : (
          <div className="text-center py-10">
            <p className="text-slate-500 text-lg">Select a component from the list to view its details.</p>
          </div>
        )}
      </section>
    </div>
  );
};
