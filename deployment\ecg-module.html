<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ECG Module - Interactive Virtual Lab</title>
    <meta name="description" content="Interactive ECG module with virtual breadboard, oscilloscope, and guided experiments for biomedical electronics learning.">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8fafc;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Header */
        .header {
            background: #ffffff;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem 0;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: bold;
            color: #2563eb;
            text-decoration: none;
        }

        .nav-menu {
            display: flex;
            list-style: none;
            gap: 2rem;
        }

        .nav-link {
            color: #374151;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s;
        }

        .nav-link:hover {
            color: #2563eb;
        }

        /* Module Header */
        .module-header {
            background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%);
            color: white;
            padding: 3rem 0;
            text-align: center;
        }

        .module-header h1 {
            font-size: 2.5rem;
            margin-bottom: 1rem;
        }

        .module-meta {
            display: flex;
            justify-content: center;
            gap: 2rem;
            margin-top: 1rem;
            flex-wrap: wrap;
        }

        .meta-item {
            background: rgba(255,255,255,0.1);
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            backdrop-filter: blur(10px);
        }

        /* Lab Interface */
        .lab-interface {
            display: grid;
            grid-template-columns: 1fr 400px;
            gap: 2rem;
            padding: 2rem 0;
            min-height: 80vh;
        }

        .main-workspace {
            background: white;
            border-radius: 1rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            overflow: hidden;
        }

        .workspace-tabs {
            display: flex;
            background: #f3f4f6;
            border-bottom: 1px solid #e5e7eb;
        }

        .tab {
            padding: 1rem 2rem;
            background: none;
            border: none;
            cursor: pointer;
            font-weight: 500;
            color: #6b7280;
            transition: all 0.3s;
        }

        .tab.active {
            background: white;
            color: #2563eb;
            border-bottom: 2px solid #2563eb;
        }

        .tab-content {
            padding: 2rem;
            min-height: 500px;
        }

        .breadboard {
            background: #2d5016;
            border-radius: 0.5rem;
            padding: 2rem;
            margin: 1rem 0;
            min-height: 300px;
            position: relative;
            background-image: 
                radial-gradient(circle at 10px 10px, #4ade80 2px, transparent 2px),
                radial-gradient(circle at 30px 10px, #4ade80 2px, transparent 2px);
            background-size: 40px 20px;
            background-repeat: repeat;
        }

        .component {
            position: absolute;
            background: #374151;
            color: white;
            padding: 0.5rem;
            border-radius: 0.25rem;
            cursor: move;
            font-size: 0.8rem;
            border: 2px solid #6b7280;
        }

        .component:hover {
            border-color: #2563eb;
        }

        .component.placed {
            border-color: #10b981;
        }

        /* Sidebar */
        .sidebar {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .sidebar-section {
            background: white;
            border-radius: 1rem;
            padding: 1.5rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        }

        .sidebar-section h3 {
            color: #2563eb;
            margin-bottom: 1rem;
            font-size: 1.1rem;
        }

        .experiment-list {
            list-style: none;
            padding: 0;
        }

        .experiment-item {
            padding: 0.75rem;
            margin: 0.5rem 0;
            background: #f8fafc;
            border-radius: 0.5rem;
            border-left: 4px solid #e5e7eb;
            cursor: pointer;
            transition: all 0.3s;
        }

        .experiment-item:hover {
            border-left-color: #2563eb;
            background: #eff6ff;
        }

        .experiment-item.active {
            border-left-color: #dc2626;
            background: #fef2f2;
        }

        .experiment-item.completed {
            border-left-color: #10b981;
            background: #f0fdf4;
        }

        .component-library {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 0.5rem;
        }

        .component-item {
            padding: 0.75rem;
            background: #f8fafc;
            border: 2px dashed #d1d5db;
            border-radius: 0.5rem;
            text-align: center;
            cursor: grab;
            transition: all 0.3s;
            font-size: 0.9rem;
        }

        .component-item:hover {
            border-color: #2563eb;
            background: #eff6ff;
        }

        .component-item:active {
            cursor: grabbing;
        }

        .instrument-panel {
            background: #1f2937;
            color: white;
            border-radius: 0.5rem;
            padding: 1rem;
            margin: 1rem 0;
        }

        .instrument-screen {
            background: #000;
            border-radius: 0.25rem;
            padding: 1rem;
            margin: 0.5rem 0;
            min-height: 150px;
            font-family: 'Courier New', monospace;
            font-size: 0.8rem;
            color: #00ff00;
        }

        .instrument-controls {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 0.5rem;
            margin-top: 1rem;
        }

        .control-knob {
            background: #374151;
            border: 2px solid #6b7280;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            cursor: pointer;
            transition: all 0.3s;
        }

        .control-knob:hover {
            border-color: #2563eb;
        }

        .btn {
            display: inline-block;
            padding: 0.75rem 1.5rem;
            border-radius: 0.5rem;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            font-size: 0.9rem;
        }

        .btn-primary {
            background: #2563eb;
            color: white;
        }

        .btn-primary:hover {
            background: #1d4ed8;
        }

        .btn-success {
            background: #10b981;
            color: white;
        }

        .btn-success:hover {
            background: #059669;
        }

        .btn-danger {
            background: #dc2626;
            color: white;
        }

        .btn-danger:hover {
            background: #b91c1c;
        }

        .progress-bar {
            background: #e5e7eb;
            border-radius: 1rem;
            height: 8px;
            overflow: hidden;
            margin: 1rem 0;
        }

        .progress-fill {
            background: #10b981;
            height: 100%;
            transition: width 0.3s ease;
        }

        /* Responsive */
        @media (max-width: 1024px) {
            .lab-interface {
                grid-template-columns: 1fr;
            }
            
            .nav-menu {
                display: none;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="nav container">
            <a href="index.html" class="logo">🔬 BioMed Virtual Lab</a>
            <ul class="nav-menu">
                <li><a href="index.html" class="nav-link">Home</a></li>
                <li><a href="virtual-lab.html" class="nav-link">Virtual Lab</a></li>
                <li><a href="curriculum.html" class="nav-link">Curriculum</a></li>
                <li><a href="pricing.html" class="nav-link">Pricing</a></li>
            </ul>
        </nav>
    </header>

    <!-- Module Header -->
    <section class="module-header">
        <div class="container">
            <h1>💓 ECG Module - Electrocardiogram</h1>
            <p>Learn ECG signal acquisition, filtering, and amplification through hands-on virtual experiments</p>
            <div class="module-meta">
                <div class="meta-item">⏱️ Duration: 6-8 hours</div>
                <div class="meta-item">🧪 6 Experiments</div>
                <div class="meta-item">📊 Real-time Analysis</div>
                <div class="meta-item">🎯 Guided Learning</div>
            </div>
        </div>
    </section>

    <!-- Lab Interface -->
    <section class="lab-interface container">
        <!-- Main Workspace -->
        <div class="main-workspace">
            <div class="workspace-tabs">
                <button class="tab active" onclick="switchTab('breadboard')">🔧 Breadboard</button>
                <button class="tab" onclick="switchTab('schematic')">📋 Schematic</button>
                <button class="tab" onclick="switchTab('oscilloscope')">📊 Oscilloscope</button>
                <button class="tab" onclick="switchTab('analysis')">📈 Analysis</button>
            </div>

            <!-- Breadboard Tab -->
            <div id="breadboard-tab" class="tab-content">
                <h3>Virtual Breadboard - ECG High-Pass Filter</h3>
                <p style="margin-bottom: 1rem; color: #6b7280;">Drag components from the library to build your circuit. Connect them by clicking and dragging between connection points.</p>
                
                <div class="breadboard">
                    <!-- Simulated breadboard with connection points -->
                    <div class="component placed" style="top: 50px; left: 100px;">Op-Amp<br>LM741</div>
                    <div class="component placed" style="top: 120px; left: 50px;">R1<br>10kΩ</div>
                    <div class="component placed" style="top: 120px; left: 200px;">C1<br>1µF</div>
                    
                    <!-- Connection wires would be drawn here -->
                    <svg style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; pointer-events: none;">
                        <line x1="150" y1="80" x2="100" y2="140" stroke="#2563eb" stroke-width="2"/>
                        <line x1="200" y1="80" x2="250" y2="140" stroke="#2563eb" stroke-width="2"/>
                    </svg>
                </div>

                <div style="display: flex; gap: 1rem; margin-top: 1rem;">
                    <button class="btn btn-primary" onclick="runSimulation()">▶️ Run Simulation</button>
                    <button class="btn btn-success" onclick="checkCircuit()">✓ Check Circuit</button>
                    <button class="btn btn-danger" onclick="clearBreadboard()">🗑️ Clear</button>
                </div>
            </div>

            <!-- Oscilloscope Tab -->
            <div id="oscilloscope-tab" class="tab-content" style="display: none;">
                <h3>Virtual Oscilloscope</h3>
                <div class="instrument-panel">
                    <div class="instrument-screen">
                        <div style="text-align: center; margin-top: 50px;">
                            ┌─────────────────────────────────────┐<br>
                            │     ECG Signal - Filtered Output    │<br>
                            │                                     │<br>
                            │    ∩     ∩     ∩     ∩     ∩      │<br>
                            │   ∕ ∖   ∕ ∖   ∕ ∖   ∕ ∖   ∕ ∖     │<br>
                            │  ∕   ∖ ∕   ∖ ∕   ∖ ∕   ∖ ∕   ∖    │<br>
                            │ ∕     ∖∕     ∖∕     ∖∕     ∖∕     ∖   │<br>
                            └─────────────────────────────────────┘<br>
                            Frequency: 1.2 Hz | Amplitude: 2.5V
                        </div>
                    </div>
                    <div class="instrument-controls">
                        <div style="text-align: center;">
                            <div class="control-knob"></div>
                            <small>V/Div</small>
                        </div>
                        <div style="text-align: center;">
                            <div class="control-knob"></div>
                            <small>Time/Div</small>
                        </div>
                        <div style="text-align: center;">
                            <div class="control-knob"></div>
                            <small>Trigger</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Analysis Tab -->
            <div id="analysis-tab" class="tab-content" style="display: none;">
                <h3>Frequency Response Analysis</h3>
                <div style="background: #f8fafc; padding: 2rem; border-radius: 0.5rem; margin: 1rem 0;">
                    <h4>High-Pass Filter Characteristics</h4>
                    <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 2rem; margin-top: 1rem;">
                        <div>
                            <strong>Measured Values:</strong>
                            <ul style="margin-top: 0.5rem;">
                                <li>Cutoff Frequency: 0.16 Hz</li>
                                <li>Gain at 1 Hz: -3.2 dB</li>
                                <li>Gain at 10 Hz: -0.1 dB</li>
                                <li>Roll-off Rate: 20 dB/decade</li>
                            </ul>
                        </div>
                        <div>
                            <strong>Theoretical Values:</strong>
                            <ul style="margin-top: 0.5rem;">
                                <li>Cutoff Frequency: 0.159 Hz</li>
                                <li>Gain at 1 Hz: -3.0 dB</li>
                                <li>Gain at 10 Hz: 0 dB</li>
                                <li>Roll-off Rate: 20 dB/decade</li>
                            </ul>
                        </div>
                    </div>
                    <div style="margin-top: 1rem;">
                        <strong>Analysis:</strong> The measured values closely match theoretical predictions, confirming proper circuit operation.
                    </div>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="sidebar">
            <!-- Experiment Progress -->
            <div class="sidebar-section">
                <h3>📋 Experiment Progress</h3>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 33%;"></div>
                </div>
                <p style="text-align: center; margin-top: 0.5rem; font-size: 0.9rem;">2 of 6 experiments completed</p>
                
                <ul class="experiment-list">
                    <li class="experiment-item completed" onclick="loadExperiment(1)">
                        <strong>1. HPF Characteristics</strong><br>
                        <small>Build and test high-pass filter</small>
                    </li>
                    <li class="experiment-item active" onclick="loadExperiment(2)">
                        <strong>2. Amplifier Design</strong><br>
                        <small>Non-inverting op-amp amplifier</small>
                    </li>
                    <li class="experiment-item" onclick="loadExperiment(3)">
                        <strong>3. LPF Characteristics</strong><br>
                        <small>Low-pass filter analysis</small>
                    </li>
                    <li class="experiment-item" onclick="loadExperiment(4)">
                        <strong>4. Band-Reject Filter</strong><br>
                        <small>60Hz notch filter design</small>
                    </li>
                    <li class="experiment-item" onclick="loadExperiment(5)">
                        <strong>5. ECG Simulator</strong><br>
                        <small>Process simulated ECG signals</small>
                    </li>
                    <li class="experiment-item" onclick="loadExperiment(6)">
                        <strong>6. Complete ECG System</strong><br>
                        <small>Full signal processing chain</small>
                    </li>
                </ul>
            </div>

            <!-- Component Library -->
            <div class="sidebar-section">
                <h3>🔧 Component Library</h3>
                <div class="component-library">
                    <div class="component-item" draggable="true">
                        📟<br>Op-Amp<br>LM741
                    </div>
                    <div class="component-item" draggable="true">
                        ⚡<br>Resistor<br>1kΩ-1MΩ
                    </div>
                    <div class="component-item" draggable="true">
                        🔋<br>Capacitor<br>1nF-100µF
                    </div>
                    <div class="component-item" draggable="true">
                        🔌<br>ECG Electrodes<br>Simulated
                    </div>
                    <div class="component-item" draggable="true">
                        ⚡<br>Power Supply<br>±15V
                    </div>
                    <div class="component-item" draggable="true">
                        📡<br>Function Gen<br>0.1Hz-1MHz
                    </div>
                </div>
            </div>

            <!-- Current Objective -->
            <div class="sidebar-section">
                <h3>🎯 Current Objective</h3>
                <div style="background: #eff6ff; padding: 1rem; border-radius: 0.5rem; border-left: 4px solid #2563eb;">
                    <strong>Build HPF Circuit</strong><br>
                    <small>Connect the op-amp, resistor, and capacitor to create a high-pass filter with cutoff frequency of 0.16 Hz.</small>
                    
                    <div style="margin-top: 1rem;">
                        <strong>Steps:</strong>
                        <ol style="margin-left: 1rem; font-size: 0.9rem;">
                            <li>Place op-amp on breadboard</li>
                            <li>Connect feedback resistor</li>
                            <li>Add input capacitor</li>
                            <li>Connect power supplies</li>
                            <li>Test with function generator</li>
                        </ol>
                    </div>
                </div>
            </div>

            <!-- Virtual Instruments -->
            <div class="sidebar-section">
                <h3>📊 Virtual Instruments</h3>
                <div style="display: flex; flex-direction: column; gap: 0.5rem;">
                    <button class="btn btn-primary" onclick="openOscilloscope()">📊 Oscilloscope</button>
                    <button class="btn btn-primary" onclick="openMultimeter()">🔌 Multimeter</button>
                    <button class="btn btn-primary" onclick="openFunctionGen()">📡 Function Generator</button>
                    <button class="btn btn-primary" onclick="openSpectrumAnalyzer()">📈 Spectrum Analyzer</button>
                </div>
            </div>
        </div>
    </section>

    <script>
        // Tab switching functionality
        function switchTab(tabName) {
            // Hide all tab contents
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.style.display = 'none';
            });
            
            // Remove active class from all tabs
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // Show selected tab and mark as active
            document.getElementById(tabName + '-tab').style.display = 'block';
            event.target.classList.add('active');
        }

        // Experiment loading
        function loadExperiment(expNum) {
            // Update active experiment
            document.querySelectorAll('.experiment-item').forEach(item => {
                item.classList.remove('active');
            });
            event.target.classList.add('active');
            
            // Update progress
            const progress = (expNum - 1) / 6 * 100;
            document.querySelector('.progress-fill').style.width = progress + '%';
            
            console.log('Loading experiment:', expNum);
        }

        // Simulation functions
        function runSimulation() {
            alert('🔬 Simulation running...\n\nCircuit analysis complete!\nHigh-pass filter is working correctly.\nCutoff frequency: 0.16 Hz');
        }

        function checkCircuit() {
            alert('✅ Circuit Check:\n\n• Op-amp connections: Correct\n• Component values: Correct\n• Power supply: Connected\n• Ready for testing!');
        }

        function clearBreadboard() {
            if (confirm('Clear all components from breadboard?')) {
                alert('🗑️ Breadboard cleared!');
            }
        }

        // Instrument functions
        function openOscilloscope() {
            switchTab('oscilloscope');
        }

        function openMultimeter() {
            alert('📊 Virtual Multimeter\n\nDC Voltage: 2.5V\nAC Voltage: 1.8V RMS\nResistance: 10.2kΩ');
        }

        function openFunctionGen() {
            alert('📡 Function Generator\n\nFrequency: 1.0 Hz\nAmplitude: 3.0V\nWaveform: Sine\nOffset: 0V');
        }

        function openSpectrumAnalyzer() {
            switchTab('analysis');
        }

        console.log('✅ ECG Virtual Lab Module loaded successfully!');
    </script>
</body>
</html>
