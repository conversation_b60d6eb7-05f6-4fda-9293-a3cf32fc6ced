<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Page Not Found - Biomedical Electronics Virtual Lab</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
        }
        
        .error-container {
            max-width: 600px;
            padding: 2rem;
        }
        
        .error-code {
            font-size: 8rem;
            font-weight: 700;
            margin-bottom: 1rem;
            opacity: 0.8;
        }
        
        .error-title {
            font-size: 2.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
        }
        
        .error-message {
            font-size: 1.25rem;
            margin-bottom: 2rem;
            opacity: 0.9;
            line-height: 1.6;
        }
        
        .error-actions {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .btn {
            display: inline-block;
            padding: 1rem 2rem;
            border-radius: 0.5rem;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }
        
        .btn-primary {
            background: white;
            color: #2563eb;
            border-color: white;
        }
        
        .btn-primary:hover {
            background: transparent;
            color: white;
            border-color: white;
            transform: translateY(-2px);
        }
        
        .btn-secondary {
            background: transparent;
            color: white;
            border-color: white;
        }
        
        .btn-secondary:hover {
            background: white;
            color: #2563eb;
            transform: translateY(-2px);
        }
        
        .circuit-animation {
            position: absolute;
            top: 20%;
            left: 10%;
            width: 80%;
            height: 60%;
            opacity: 0.1;
            pointer-events: none;
            overflow: hidden;
        }
        
        .circuit-line {
            position: absolute;
            background: white;
            animation: pulse 3s ease-in-out infinite;
        }
        
        .line-1 {
            width: 100px;
            height: 2px;
            top: 20%;
            left: 20%;
            animation-delay: 0s;
        }
        
        .line-2 {
            width: 2px;
            height: 80px;
            top: 30%;
            left: 40%;
            animation-delay: 0.5s;
        }
        
        .line-3 {
            width: 120px;
            height: 2px;
            top: 60%;
            left: 50%;
            animation-delay: 1s;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 0.1; }
            50% { opacity: 0.3; }
        }
        
        @media (max-width: 768px) {
            .error-code {
                font-size: 6rem;
            }
            
            .error-title {
                font-size: 2rem;
            }
            
            .error-message {
                font-size: 1.1rem;
            }
            
            .error-actions {
                flex-direction: column;
                align-items: center;
            }
            
            .btn {
                width: 250px;
            }
        }
    </style>
</head>
<body>
    <div class="circuit-animation">
        <div class="circuit-line line-1"></div>
        <div class="circuit-line line-2"></div>
        <div class="circuit-line line-3"></div>
    </div>
    
    <div class="error-container">
        <div class="error-code">404</div>
        <h1 class="error-title">Circuit Not Found</h1>
        <p class="error-message">
            Looks like this page has been disconnected from our virtual lab. 
            Don't worry - let's get you back to exploring biomedical electronics!
        </p>
        <div class="error-actions">
            <a href="/" class="btn btn-primary">
                <i class="fas fa-home"></i> Back to Home
            </a>
            <a href="/curriculum" class="btn btn-secondary">
                <i class="fas fa-book"></i> View Curriculum
            </a>
        </div>
    </div>
</body>
</html>
