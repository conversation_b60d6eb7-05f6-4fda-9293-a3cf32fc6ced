<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Curriculum Details - Biomedical Electronics Virtual Lab</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Navigation */
        .navbar {
            background: white;
            padding: 1rem 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .nav-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .nav-logo {
            display: flex;
            align-items: center;
            font-size: 1.5rem;
            font-weight: 700;
            color: #2563eb;
        }

        .nav-logo i {
            margin-right: 0.5rem;
        }

        .nav-menu {
            display: flex;
            list-style: none;
            gap: 2rem;
        }

        .nav-link {
            text-decoration: none;
            color: #374151;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .nav-link:hover {
            color: #2563eb;
        }

        .cta-button {
            background: #2563eb;
            color: white !important;
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
        }

        /* Header */
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 4rem 0;
            text-align: center;
        }

        .header h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
        }

        .header p {
            font-size: 1.25rem;
            opacity: 0.9;
            max-width: 600px;
            margin: 0 auto;
        }

        /* Module Cards */
        .modules-section {
            padding: 4rem 0;
        }

        .section-title {
            font-size: 2.5rem;
            text-align: center;
            margin-bottom: 3rem;
            color: #1f2937;
        }

        .module-card {
            background: white;
            border-radius: 1rem;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            border-left: 4px solid #2563eb;
            transition: transform 0.3s ease;
        }

        .module-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        .module-header {
            display: flex;
            align-items: center;
            margin-bottom: 1.5rem;
        }

        .module-number {
            background: #2563eb;
            color: white;
            width: 3rem;
            height: 3rem;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            margin-right: 1rem;
        }

        .module-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #1f2937;
        }

        .module-meta {
            display: flex;
            gap: 2rem;
            margin-bottom: 1.5rem;
            flex-wrap: wrap;
        }

        .meta-item {
            display: flex;
            align-items: center;
            color: #6b7280;
            font-size: 0.9rem;
        }

        .meta-item i {
            margin-right: 0.5rem;
            color: #2563eb;
        }

        .module-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }

        .content-section h4 {
            color: #1f2937;
            margin-bottom: 1rem;
            font-weight: 600;
        }

        .content-list {
            list-style: none;
            padding: 0;
        }

        .content-list li {
            padding: 0.5rem 0;
            padding-left: 1.5rem;
            position: relative;
            color: #374151;
        }

        .content-list li::before {
            content: '✓';
            position: absolute;
            left: 0;
            color: #10b981;
            font-weight: bold;
        }

        .experiments-section {
            background: #f9fafb;
            padding: 1.5rem;
            border-radius: 0.5rem;
            margin-top: 1.5rem;
        }

        .experiment-item {
            margin-bottom: 1rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid #e5e7eb;
        }

        .experiment-item:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }

        .experiment-title {
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 0.5rem;
        }

        .applications-section {
            background: #eff6ff;
            padding: 1.5rem;
            border-radius: 0.5rem;
            border-left: 4px solid #3b82f6;
            margin-top: 1.5rem;
        }

        .cta-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 3rem;
            border-radius: 1rem;
            text-align: center;
            margin: 3rem 0;
        }

        .btn {
            display: inline-block;
            padding: 1rem 2rem;
            border-radius: 0.5rem;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            margin: 0.5rem;
        }

        .btn-primary {
            background: white;
            color: #2563eb;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(255, 255, 255, 0.3);
        }

        /* Footer */
        .footer {
            background: #1f2937;
            color: white;
            padding: 3rem 0 1rem;
            text-align: center;
        }

        .footer p {
            margin: 0.5rem 0;
            opacity: 0.8;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .nav-menu {
                display: none;
            }

            .header h1 {
                font-size: 2rem;
            }

            .module-header {
                flex-direction: column;
                text-align: center;
            }

            .module-number {
                margin-right: 0;
                margin-bottom: 1rem;
            }

            .module-meta {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container container">
            <div class="nav-logo">
                <i class="fas fa-heartbeat"></i>
                <span>BioMed Virtual Lab</span>
            </div>
            <ul class="nav-menu">
                <li><a href="index-fixed.html" class="nav-link">Home</a></li>
                <li><a href="#modules" class="nav-link">Modules</a></li>
                <li><a href="pricing-fixed.html" class="nav-link">Pricing</a></li>
                <li><a href="mailto:<EMAIL>?subject=Demo Request" class="nav-link cta-button">Request Demo</a></li>
            </ul>
        </div>
    </nav>

    <!-- Header -->
    <section class="header">
        <div class="container">
            <h1>Complete Curriculum Overview</h1>
            <p>Our comprehensive curriculum takes you from basic circuit analysis to advanced biomedical system design. Each module builds upon previous knowledge while introducing real-world medical device applications.</p>
        </div>
    </section>

    <!-- Modules Section -->
    <section id="modules" class="modules-section">
        <div class="container">
            <h2 class="section-title">Learning Modules</h2>

            <!-- Module 1 -->
            <div class="module-card">
                <div class="module-header">
                    <div class="module-number">1</div>
                    <div>
                        <div class="module-title">DC & AC Circuit Fundamentals</div>
                        <p style="color: #6b7280; margin: 0;">Foundation of all biomedical electronics</p>
                    </div>
                </div>
                
                <div class="module-meta">
                    <div class="meta-item">
                        <i class="fas fa-clock"></i>
                        <span>Duration: 4-6 hours</span>
                    </div>
                    <div class="meta-item">
                        <i class="fas fa-layer-group"></i>
                        <span>Prerequisites: Basic mathematics</span>
                    </div>
                    <div class="meta-item">
                        <i class="fas fa-flask"></i>
                        <span>4 hands-on experiments</span>
                    </div>
                </div>

                <div class="module-content">
                    <div class="content-section">
                        <h4>Learning Objectives</h4>
                        <ul class="content-list">
                            <li>Apply Ohm's Law to medical device circuits</li>
                            <li>Analyze series/parallel combinations in monitoring systems</li>
                            <li>Calculate power for battery-operated devices</li>
                            <li>Master AC concepts and phase relationships</li>
                            <li>Understand impedance in biological tissue</li>
                        </ul>
                    </div>
                    <div class="content-section">
                        <h4>Key Skills Developed</h4>
                        <ul class="content-list">
                            <li>Circuit analysis and problem-solving</li>
                            <li>Multimeter usage and measurement techniques</li>
                            <li>Safety calculations for patient protection</li>
                            <li>Power supply design principles</li>
                            <li>Frequency response analysis</li>
                        </ul>
                    </div>
                </div>

                <div class="experiments-section">
                    <h4>Hands-On Experiments</h4>
                    <div class="experiment-item">
                        <div class="experiment-title">1. Patient Safety Resistor Analysis</div>
                        <p>Calculate and verify current limiting resistors for ECG electrodes to ensure patient safety under fault conditions.</p>
                    </div>
                    <div class="experiment-item">
                        <div class="experiment-title">2. Medical Device Power Supply Design</div>
                        <p>Design regulated power supplies for medical instruments with proper isolation and safety margins.</p>
                    </div>
                    <div class="experiment-item">
                        <div class="experiment-title">3. Skin-Electrode Impedance Measurement</div>
                        <p>Measure and analyze impedance characteristics at different frequencies to optimize electrode design.</p>
                    </div>
                    <div class="experiment-item">
                        <div class="experiment-title">4. Signal Generator for Biomedical Testing</div>
                        <p>Create test signals that simulate physiological waveforms for equipment calibration and testing.</p>
                    </div>
                </div>

                <div class="applications-section">
                    <h4><i class="fas fa-hospital"></i> Real-World Medical Applications</h4>
                    <ul style="margin: 0; padding-left: 1.5rem;">
                        <li>Defibrillator energy calculations and safety analysis</li>
                        <li>Pacemaker battery life estimation and optimization</li>
                        <li>ECG electrode impedance matching for signal quality</li>
                        <li>Medical device power consumption analysis and efficiency</li>
                    </ul>
                </div>
            </div>

            <!-- Module 4 -->
            <div class="module-card">
                <div class="module-header">
                    <div class="module-number">4</div>
                    <div>
                        <div class="module-title">Operational Amplifiers</div>
                        <p style="color: #6b7280; margin: 0;">The workhorse of biomedical instrumentation</p>
                    </div>
                </div>
                
                <div class="module-meta">
                    <div class="meta-item">
                        <i class="fas fa-clock"></i>
                        <span>Duration: 8-10 hours</span>
                    </div>
                    <div class="meta-item">
                        <i class="fas fa-layer-group"></i>
                        <span>Prerequisites: Modules 1-3</span>
                    </div>
                    <div class="meta-item">
                        <i class="fas fa-flask"></i>
                        <span>5 advanced experiments</span>
                    </div>
                </div>

                <div class="module-content">
                    <div class="content-section">
                        <h4>Advanced Learning Objectives</h4>
                        <ul class="content-list">
                            <li>Design precision instrumentation amplifiers</li>
                            <li>Master common-mode rejection techniques</li>
                            <li>Understand op-amp limitations and compensation</li>
                            <li>Implement active filtering solutions</li>
                            <li>Analyze noise and offset voltage effects</li>
                        </ul>
                    </div>
                    <div class="content-section">
                        <h4>Clinical Applications</h4>
                        <ul class="content-list">
                            <li>ECG signal acquisition and conditioning</li>
                            <li>Common-mode noise rejection in clinical environments</li>
                            <li>Right leg drive circuit implementation</li>
                            <li>Precision measurement of bio-potentials</li>
                            <li>Medical device safety and isolation</li>
                        </ul>
                    </div>
                </div>

                <div class="experiments-section">
                    <h4>Advanced Experiments</h4>
                    <div class="experiment-item">
                        <div class="experiment-title">1. Complete ECG Instrumentation Amplifier</div>
                        <p>Build and test a 3-op-amp instrumentation amplifier with adjustable gain and high CMRR for ECG applications.</p>
                    </div>
                    <div class="experiment-item">
                        <div class="experiment-title">2. Common-Mode Rejection Optimization</div>
                        <p>Measure and optimize CMRR performance, focusing on 60Hz power line interference rejection.</p>
                    </div>
                    <div class="experiment-item">
                        <div class="experiment-title">3. Right Leg Drive Circuit Implementation</div>
                        <p>Design and implement active common-mode cancellation using driven right leg techniques.</p>
                    </div>
                </div>

                <div class="applications-section">
                    <h4><i class="fas fa-heartbeat"></i> Clinical Context & Standards</h4>
                    <ul style="margin: 0; padding-left: 1.5rem;">
                        <li>Understanding how instrumentation amplifiers enable safe ECG measurement</li>
                        <li>Why CMRR is critical for patient safety and signal quality</li>
                        <li>Identifying and mitigating noise sources in clinical environments</li>
                        <li>Designing circuits that meet IEC 60601 medical device safety standards</li>
                    </ul>
                </div>
            </div>

            <!-- CTA Section -->
            <div class="cta-section">
                <h3 style="margin-bottom: 1rem;">Ready to Start Your Learning Journey?</h3>
                <p style="margin-bottom: 2rem; opacity: 0.9;">Experience the complete curriculum with hands-on experiments and real-world applications.</p>
                <a href="mailto:<EMAIL>?subject=Demo Request" class="btn btn-primary">Request a Demo</a>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <h3>Biomedical Electronics Virtual Lab</h3>
            <p>Developed by Dr. Mohammed Yagoub Esmail, SUST - BME</p>
            <p>Email: <EMAIL></p>
            <p>Phone: +249 912 867 327 | +966 538 076 790</p>
            <p>&copy; 2025. All Rights Reserved.</p>
        </div>
    </footer>

    <script>
        // Smooth scrolling
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        console.log('✅ Curriculum page loaded successfully!');
    </script>
</body>
</html>
