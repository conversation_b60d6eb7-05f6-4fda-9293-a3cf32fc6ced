<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Biomedical Electronics Virtual Lab - Master Medical Device Electronics</title>
    <meta name="description" content="Master biomedical electronics with our interactive virtual lab. Build, test, and troubleshoot ECG circuits, op-amps, and medical device electronics safely.">
    
    <!-- Embedded CSS for guaranteed display -->
    <style>
        /* Reset and Base Styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #ffffff;
        }

        /* Container */
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Header and Navigation */
        .header {
            background: #ffffff;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            position: fixed;
            top: 0;
            width: 100%;
            z-index: 1000;
        }

        .nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem 0;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: bold;
            color: #2563eb;
            text-decoration: none;
        }

        .nav-menu {
            display: flex;
            list-style: none;
            gap: 2rem;
        }

        .nav-link {
            color: #374151;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s;
        }

        .nav-link:hover {
            color: #2563eb;
        }

        .cta-nav {
            background: #2563eb;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            text-decoration: none;
            transition: background 0.3s;
        }

        .cta-nav:hover {
            background: #1d4ed8;
        }

        /* Hero Section */
        .hero {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 120px 0 80px;
            text-align: center;
            min-height: 100vh;
            display: flex;
            align-items: center;
        }

        .hero-content h1 {
            font-size: 3.5rem;
            font-weight: bold;
            margin-bottom: 1.5rem;
            line-height: 1.2;
        }

        .hero-content p {
            font-size: 1.25rem;
            margin-bottom: 2rem;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
            opacity: 0.95;
        }

        .hero-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }

        /* Buttons */
        .btn {
            display: inline-block;
            padding: 1rem 2rem;
            border-radius: 0.5rem;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            border: 2px solid transparent;
            cursor: pointer;
        }

        .btn-primary {
            background: #2563eb;
            color: white;
            border-color: #2563eb;
        }

        .btn-primary:hover {
            background: #1d4ed8;
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(37, 99, 235, 0.3);
        }

        .btn-secondary {
            background: transparent;
            color: white;
            border-color: white;
        }

        .btn-secondary:hover {
            background: white;
            color: #2563eb;
            transform: translateY(-2px);
        }

        /* Features Section */
        .features {
            padding: 5rem 0;
            background: #f9fafb;
        }

        .section-title {
            font-size: 2.5rem;
            font-weight: bold;
            text-align: center;
            margin-bottom: 3rem;
            color: #1f2937;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }

        .feature-card {
            background: white;
            padding: 2rem;
            border-radius: 1rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            text-align: center;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 25px rgba(0, 0, 0, 0.1);
        }

        .feature-icon {
            width: 4rem;
            height: 4rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            color: white;
            font-size: 1.5rem;
        }

        .feature-card h3 {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: #1f2937;
        }

        .feature-card p {
            color: #6b7280;
            line-height: 1.6;
        }

        /* Curriculum Section */
        .curriculum {
            padding: 5rem 0;
            background: white;
        }

        .curriculum-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-top: 3rem;
        }

        .module-card {
            background: #f8fafc;
            padding: 1.5rem;
            border-radius: 0.5rem;
            border-left: 4px solid #2563eb;
        }

        .module-card h4 {
            color: #2563eb;
            margin-bottom: 0.5rem;
        }

        /* Pricing Section */
        .pricing {
            padding: 5rem 0;
            background: #f9fafb;
        }

        .pricing-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }

        .pricing-card {
            background: white;
            padding: 2rem;
            border-radius: 1rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            text-align: center;
            position: relative;
        }

        .pricing-card.featured {
            border: 3px solid #2563eb;
            transform: scale(1.05);
        }

        .pricing-card.featured::before {
            content: 'Most Popular';
            position: absolute;
            top: -15px;
            left: 50%;
            transform: translateX(-50%);
            background: #2563eb;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 1rem;
            font-size: 0.875rem;
            font-weight: 600;
        }

        .plan-name {
            font-size: 1.5rem;
            font-weight: bold;
            color: #1f2937;
            margin-bottom: 0.5rem;
        }

        .plan-price {
            font-size: 3rem;
            font-weight: bold;
            color: #2563eb;
            margin: 1rem 0;
        }

        .plan-features {
            list-style: none;
            margin: 2rem 0;
            text-align: left;
        }

        .plan-features li {
            padding: 0.5rem 0;
            border-bottom: 1px solid #f3f4f6;
        }

        .plan-features li::before {
            content: '✓';
            color: #10b981;
            font-weight: bold;
            margin-right: 0.5rem;
        }

        /* CTA Section */
        .cta-section {
            padding: 5rem 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
        }

        .cta-section h2 {
            font-size: 2.5rem;
            margin-bottom: 1rem;
        }

        .cta-section p {
            font-size: 1.25rem;
            margin-bottom: 2rem;
            opacity: 0.95;
        }

        /* Footer */
        .footer {
            background: #1f2937;
            color: white;
            padding: 3rem 0 1rem;
            text-align: center;
        }

        .footer h3 {
            margin-bottom: 1rem;
        }

        .footer p {
            margin: 0.5rem 0;
            opacity: 0.8;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .nav-menu {
                display: none;
            }

            .hero-content h1 {
                font-size: 2.5rem;
            }

            .hero-buttons {
                flex-direction: column;
                align-items: center;
            }

            .btn {
                width: 250px;
                text-align: center;
            }

            .pricing-card.featured {
                transform: none;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="nav container">
            <a href="#" class="logo">🔬 BioMed Virtual Lab</a>
            <ul class="nav-menu">
                <li><a href="#features" class="nav-link">Features</a></li>
                <li><a href="#curriculum" class="nav-link">Curriculum</a></li>
                <li><a href="#pricing" class="nav-link">Pricing</a></li>
                <li><a href="#contact" class="nav-link">Contact</a></li>
                <li><a href="mailto:<EMAIL>?subject=Demo Request" class="cta-nav">Request Demo</a></li>
            </ul>
        </nav>
    </header>

    <!-- Hero Section -->
    <section class="hero">
        <div class="container">
            <div class="hero-content">
                <h1>Master the Electronics Behind Modern Medicine</h1>
                <p>Build, test, and troubleshoot the essential circuits that power life-saving medical devices. Our fully interactive virtual lab bridges the gap between theory and real-world application.</p>
                <div class="hero-buttons">
                    <a href="mailto:<EMAIL>?subject=Demo Request" class="btn btn-primary">Request a Demo</a>
                    <a href="#curriculum" class="btn btn-secondary">View Curriculum</a>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="features">
        <div class="container">
            <h2 class="section-title">Why Our Virtual Lab?</h2>
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">🔧</div>
                    <h3>Interactive Circuit Building</h3>
                    <p>Drag and drop components to build complex biomedical circuits. From basic op-amps to complete ECG systems, experience hands-on learning without physical constraints.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">📊</div>
                    <h3>Real-Time Signal Analysis</h3>
                    <p>Watch signals flow through your circuits with our advanced oscilloscope and spectrum analyzer. Understand how each component affects the final output.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">🛠️</div>
                    <h3>Fault Simulation & Troubleshooting</h3>
                    <p>Practice diagnosing real-world problems with our intelligent fault injection system. Build the critical thinking skills essential for medical device engineering.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">🎓</div>
                    <h3>Structured Learning Path</h3>
                    <p>Progress through 10 comprehensive modules, from DC fundamentals to advanced biomedical applications. Each lesson builds practical skills you'll use in your career.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">👥</div>
                    <h3>Educator Dashboard</h3>
                    <p>Track student progress, assign custom exercises, and monitor learning outcomes. Perfect for classroom integration and remote learning environments.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">📱</div>
                    <h3>Anywhere, Anytime Access</h3>
                    <p>Learn on any device, anywhere. Our cloud-based platform ensures your progress is always saved and accessible across all your devices.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Curriculum Section -->
    <section id="curriculum" class="curriculum">
        <div class="container">
            <h2 class="section-title">Complete Curriculum</h2>
            <p style="text-align: center; font-size: 1.1rem; color: #6b7280; margin-bottom: 2rem;">10 comprehensive modules covering everything from basic circuits to advanced medical device design</p>
            <div class="curriculum-grid">
                <div class="module-card">
                    <h4>Module 1: DC & AC Fundamentals</h4>
                    <p>Foundation of all biomedical electronics</p>
                </div>
                <div class="module-card">
                    <h4>Module 4: Operational Amplifiers</h4>
                    <p>The workhorse of biomedical instrumentation</p>
                </div>
                <div class="module-card">
                    <h4>Module 7: ECG Signal Processing</h4>
                    <p>Complete cardiac monitoring systems</p>
                </div>
                <div class="module-card">
                    <h4>Module 10: Medical Device Safety</h4>
                    <p>Patient protection and regulatory compliance</p>
                </div>
            </div>
            <div style="text-align: center; margin-top: 2rem;">
                <a href="curriculum.html" class="btn btn-primary">View Complete Curriculum</a>
            </div>
        </div>
    </section>

    <!-- Pricing Section -->
    <section id="pricing" class="pricing">
        <div class="container">
            <h2 class="section-title">Choose Your Learning Path</h2>
            <div class="pricing-grid">
                <div class="pricing-card">
                    <div class="plan-name">Student</div>
                    <div class="plan-price">$29<span style="font-size: 1rem; color: #6b7280;">/month</span></div>
                    <ul class="plan-features">
                        <li>Access to all 10 modules</li>
                        <li>Unlimited circuit building</li>
                        <li>Progress tracking</li>
                        <li>Community forum access</li>
                    </ul>
                    <a href="mailto:<EMAIL>?subject=Student Plan Interest" class="btn btn-primary">Start Free Trial</a>
                </div>
                <div class="pricing-card featured">
                    <div class="plan-name">Educator</div>
                    <div class="plan-price">$99<span style="font-size: 1rem; color: #6b7280;">/month</span></div>
                    <ul class="plan-features">
                        <li>Everything in Student plan</li>
                        <li>Up to 50 student accounts</li>
                        <li>Educator dashboard</li>
                        <li>Assignment creation tools</li>
                        <li>Advanced analytics</li>
                    </ul>
                    <a href="mailto:<EMAIL>?subject=Educator Plan Interest" class="btn btn-primary">Start Free Trial</a>
                </div>
                <div class="pricing-card">
                    <div class="plan-name">Professional</div>
                    <div class="plan-price">$49<span style="font-size: 1rem; color: #6b7280;">/month</span></div>
                    <ul class="plan-features">
                        <li>Everything in Student plan</li>
                        <li>Advanced troubleshooting</li>
                        <li>Industry-specific modules</li>
                        <li>Certification tracking</li>
                        <li>Professional community</li>
                    </ul>
                    <a href="mailto:<EMAIL>?subject=Professional Plan Interest" class="btn btn-primary">Start Free Trial</a>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section id="contact" class="cta-section">
        <div class="container">
            <h2>Ready to Transform Your Learning?</h2>
            <p>Join thousands of students and educators who are mastering biomedical electronics with our virtual lab.</p>
            <div class="hero-buttons">
                <a href="mailto:<EMAIL>?subject=Demo Request" class="btn btn-primary">Request Your Free Demo</a>
                <a href="pricing.html" class="btn btn-secondary">View All Plans</a>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <h3>Biomedical Electronics Virtual Lab</h3>
            <p>Developed by Dr. Mohammed Yagoub Esmail, SUST - BME</p>
            <p>📧 Email: <EMAIL></p>
            <p>📞 Phone: +249 912 867 327 | +966 538 076 790</p>
            <p>&copy; 2025. All Rights Reserved.</p>
        </div>
    </footer>

    <!-- JavaScript -->
    <script>
        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Header background change on scroll
        window.addEventListener('scroll', function() {
            const header = document.querySelector('.header');
            if (window.scrollY > 100) {
                header.style.background = 'rgba(255, 255, 255, 0.98)';
                header.style.backdropFilter = 'blur(10px)';
            } else {
                header.style.background = '#ffffff';
                header.style.backdropFilter = 'none';
            }
        });

        console.log('✅ Biomedical Electronics Virtual Lab - Website loaded successfully!');
    </script>
</body>
</html>
