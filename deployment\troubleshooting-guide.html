<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Troubleshooting Guide - Biomedical Electronics Virtual Lab</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
            line-height: 1.6;
        }
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2563eb;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5rem;
        }
        h2 {
            color: #1f2937;
            border-bottom: 2px solid #2563eb;
            padding-bottom: 10px;
            margin-top: 40px;
        }
        h3 {
            color: #374151;
            margin-top: 30px;
        }
        .status-check {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .solution {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 15px;
            margin: 15px 0;
        }
        .code {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            overflow-x: auto;
        }
        .btn {
            display: inline-block;
            background: #2563eb;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 5px;
            margin: 10px 5px;
            font-weight: 600;
        }
        .btn:hover {
            background: #1d4ed8;
        }
        .btn-success {
            background: #28a745;
        }
        .btn-warning {
            background: #ffc107;
            color: #212529;
        }
        .checklist {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .checklist ul {
            list-style-type: none;
            padding: 0;
        }
        .checklist li {
            padding: 8px 0;
            border-bottom: 1px solid #dee2e6;
        }
        .checklist li:last-child {
            border-bottom: none;
        }
        .checklist li::before {
            content: '☐ ';
            margin-right: 10px;
            font-size: 1.2em;
        }
        .icon {
            margin-right: 10px;
        }
        .success { color: #28a745; }
        .warning-text { color: #ffc107; }
        .error-text { color: #dc3545; }
        .info { color: #17a2b8; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Website Troubleshooting Guide</h1>
        
        <div class="status-check">
            <h3><span class="icon">✅</span>Quick Status Check</h3>
            <p><strong>If you can see this page, your web server is working!</strong></p>
            <p>This means the basic file upload and server configuration are functioning correctly.</p>
        </div>

        <h2>🚨 Common Issues & Solutions</h2>

        <h3>Issue 1: Blank/Empty Page Display</h3>
        <div class="error">
            <strong>Symptoms:</strong> Page loads but shows completely blank or white screen
        </div>
        
        <div class="solution">
            <h4>Solution Steps:</h4>
            <ol>
                <li><strong>Check Browser Console:</strong>
                    <ul>
                        <li>Press F12 to open Developer Tools</li>
                        <li>Click "Console" tab</li>
                        <li>Look for red error messages</li>
                        <li>Common errors: "Failed to load resource" or "CSS/JS file not found"</li>
                    </ul>
                </li>
                <li><strong>Verify File Structure:</strong>
                    <div class="code">
/public_html (or your web root)
├── index.html
├── curriculum.html  
├── pricing.html
├── test.html
├── assets/
│   ├── css/
│   │   └── styles.css
│   └── js/
│       ├── script.js
│       └── analytics-tracking.js
├── .htaccess
├── robots.txt
└── sitemap.xml</div>
                </li>
                <li><strong>Check File Permissions:</strong>
                    <ul>
                        <li>Files should be 644 (readable by web server)</li>
                        <li>Directories should be 755 (executable by web server)</li>
                    </ul>
                </li>
            </ol>
        </div>

        <h3>Issue 2: CSS Styles Not Loading</h3>
        <div class="warning">
            <strong>Symptoms:</strong> Page content appears but without styling (plain text, no colors/layout)
        </div>
        
        <div class="solution">
            <h4>Quick Fixes:</h4>
            <ol>
                <li><strong>Use Self-Contained Files:</strong>
                    <p>We've created fixed versions with embedded CSS that will always work:</p>
                    <ul>
                        <li><code>index-fixed.html</code> - Main landing page</li>
                        <li><code>curriculum-fixed.html</code> - Curriculum details</li>
                        <li><code>pricing-fixed.html</code> - Pricing page</li>
                    </ul>
                </li>
                <li><strong>Rename Fixed Files:</strong>
                    <div class="code">
Rename index-fixed.html to index.html
Rename curriculum-fixed.html to curriculum.html  
Rename pricing-fixed.html to pricing.html</div>
                </li>
                <li><strong>Clear Browser Cache:</strong>
                    <ul>
                        <li>Press Ctrl+F5 (Windows) or Cmd+Shift+R (Mac)</li>
                        <li>Or use incognito/private browsing mode</li>
                    </ul>
                </li>
            </ol>
        </div>

        <h3>Issue 3: Navigation Links Not Working</h3>
        <div class="warning">
            <strong>Symptoms:</strong> Clicking menu items shows "Page Not Found" errors
        </div>
        
        <div class="solution">
            <h4>Solutions:</h4>
            <ol>
                <li><strong>Check File Names:</strong>
                    <p>Ensure these files exist in your web root:</p>
                    <ul>
                        <li><code>index.html</code> (homepage)</li>
                        <li><code>curriculum.html</code> (curriculum page)</li>
                        <li><code>pricing.html</code> (pricing page)</li>
                    </ul>
                </li>
                <li><strong>Case Sensitivity:</strong>
                    <p>On Linux servers, file names are case-sensitive. Ensure exact spelling and capitalization.</p>
                </li>
                <li><strong>URL Structure:</strong>
                    <p>Links should work as:</p>
                    <ul>
                        <li><code>yoursite.com/</code> → index.html</li>
                        <li><code>yoursite.com/curriculum.html</code> → curriculum page</li>
                        <li><code>yoursite.com/pricing.html</code> → pricing page</li>
                    </ul>
                </li>
            </ol>
        </div>

        <h2>🔍 Diagnostic Checklist</h2>
        
        <div class="checklist">
            <h3>Pre-Upload Checklist:</h3>
            <ul>
                <li>All HTML files are present (index.html, curriculum.html, pricing.html)</li>
                <li>Assets folder contains css/ and js/ subdirectories</li>
                <li>CSS file (styles.css) is in assets/css/ folder</li>
                <li>JavaScript files are in assets/js/ folder</li>
                <li>Configuration files (.htaccess, robots.txt, sitemap.xml) are included</li>
            </ul>
        </div>

        <div class="checklist">
            <h3>Post-Upload Checklist:</h3>
            <ul>
                <li>Domain DNS is pointing to correct hosting server</li>
                <li>SSL certificate is installed and active (https://)</li>
                <li>File permissions are set correctly (644 for files, 755 for directories)</li>
                <li>All files uploaded to correct directory (usually public_html or www)</li>
                <li>Test pages load in different browsers (Chrome, Firefox, Safari, Edge)</li>
            </ul>
        </div>

        <h2>🧪 Testing Your Website</h2>

        <h3>Quick Tests:</h3>
        <div style="text-align: center; margin: 30px 0;">
            <a href="index.html" class="btn">🏠 Test Main Page</a>
            <a href="curriculum.html" class="btn">📚 Test Curriculum</a>
            <a href="pricing.html" class="btn">💰 Test Pricing</a>
        </div>

        <div style="text-align: center; margin: 30px 0;">
            <a href="index-fixed.html" class="btn btn-success">🏠 Backup Main Page</a>
            <a href="curriculum-fixed.html" class="btn btn-success">📚 Backup Curriculum</a>
            <a href="pricing-fixed.html" class="btn btn-success">💰 Backup Pricing</a>
        </div>

        <h3>Browser Console Check:</h3>
        <div class="code">
1. Press F12 to open Developer Tools
2. Click "Console" tab  
3. Refresh the page (F5)
4. Look for any red error messages
5. Common issues:
   - "Failed to load resource: assets/css/styles.css"
   - "Uncaught ReferenceError"
   - "Mixed Content" warnings (HTTP vs HTTPS)
</div>

        <h2>🆘 Emergency Solutions</h2>

        <h3>If Nothing Works:</h3>
        <div class="solution">
            <ol>
                <li><strong>Use the Fixed Files:</strong>
                    <p>The files ending in "-fixed.html" are completely self-contained and will always work:</p>
                    <ul>
                        <li>Rename <code>index-fixed.html</code> to <code>index.html</code></li>
                        <li>Rename <code>curriculum-fixed.html</code> to <code>curriculum.html</code></li>
                        <li>Rename <code>pricing-fixed.html</code> to <code>pricing.html</code></li>
                    </ul>
                </li>
                <li><strong>Contact Hosting Support:</strong>
                    <p>If files still don't display, contact your hosting provider's technical support with these details:</p>
                    <ul>
                        <li>Domain name</li>
                        <li>Error messages from browser console</li>
                        <li>Screenshots of the issue</li>
                    </ul>
                </li>
                <li><strong>Alternative Hosting:</strong>
                    <p>Try these free hosting options for quick deployment:</p>
                    <ul>
                        <li><strong>Netlify:</strong> Drag and drop your files at netlify.com</li>
                        <li><strong>Vercel:</strong> Upload files at vercel.com</li>
                        <li><strong>GitHub Pages:</strong> Free hosting for static websites</li>
                    </ul>
                </li>
            </ol>
        </div>

        <h2>📞 Get Help</h2>
        
        <div style="background: #e3f2fd; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3 style="color: #1565c0; margin-top: 0;">Contact Information</h3>
            <p><strong>Developer:</strong> Dr. Mohammed Yagoub Esmail, SUST - BME</p>
            <p><strong>Email:</strong> <a href="mailto:<EMAIL>"><EMAIL></a></p>
            <p><strong>Phone:</strong> +249 912 867 327 | +966 538 076 790</p>
            
            <h4>When contacting for support, please include:</h4>
            <ul>
                <li>Your domain name or hosting provider</li>
                <li>Screenshots of any error messages</li>
                <li>Browser console errors (F12 → Console tab)</li>
                <li>Which files you've uploaded and where</li>
            </ul>
        </div>

        <div style="text-align: center; margin: 40px 0;">
            <p><strong>Last Updated:</strong> <span id="datetime"></span></p>
            <p><small>Biomedical Electronics Virtual Lab - Troubleshooting Guide</small></p>
        </div>
    </div>

    <script>
        // Display current date and time
        document.getElementById('datetime').textContent = new Date().toLocaleString();
        
        // Test JavaScript functionality
        console.log('✅ Troubleshooting guide loaded successfully');
        console.log('✅ JavaScript is working correctly');
        
        // Add click handlers for checklist items
        document.querySelectorAll('.checklist li').forEach(item => {
            item.addEventListener('click', function() {
                if (this.style.textDecoration === 'line-through') {
                    this.style.textDecoration = 'none';
                    this.style.opacity = '1';
                    this.innerHTML = this.innerHTML.replace('☑', '☐');
                } else {
                    this.style.textDecoration = 'line-through';
                    this.style.opacity = '0.6';
                    this.innerHTML = this.innerHTML.replace('☐', '☑');
                }
            });
        });
    </script>
</body>
</html>
