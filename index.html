
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Biomedical Device Simulator</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <style>
    /* Custom scrollbar for better aesthetics if needed, but Tailwind usually handles this well */
    body {
      font-family: 'Inter', sans-serif; /* Using a common sans-serif font */
    }
    /* For Recharts specifically, to ensure labels are visible */
    .recharts-cartesian-axis-tick-value tspan {
      font-size: 0.75rem; /* 12px */
      fill: #4A5568; /* gray-700 */
    }
    .recharts-tooltip-wrapper {
        outline: none !important;
    }
  </style>
<script type="importmap">
{
  "imports": {
    "react/": "https://esm.sh/react@^19.1.0/",
    "react": "https://esm.sh/react@^19.1.0",
    "react-dom/": "https://esm.sh/react-dom@^19.1.0/",
    "@google/genai": "https://esm.sh/@google/genai@^1.0.1",
    "recharts": "https://esm.sh/recharts@^2.15.3"
  }
}
</script>
</head>
<body class="bg-slate-100">
  <div id="root"></div>
  <script type="module" src="/index.tsx"></script>
</body>
</html>
<link rel="stylesheet" href="index.css">
<script src="index.tsx" type="module"></script>
