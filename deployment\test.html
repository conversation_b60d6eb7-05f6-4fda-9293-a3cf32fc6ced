<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Page - Biomedical Electronics Virtual Lab</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2563eb;
            text-align: center;
            margin-bottom: 30px;
        }
        .status {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .btn {
            display: inline-block;
            background: #2563eb;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 5px;
            margin: 10px 5px;
        }
        .btn:hover {
            background: #1d4ed8;
        }
        .file-list {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .file-list ul {
            list-style-type: none;
            padding: 0;
        }
        .file-list li {
            padding: 5px 0;
            border-bottom: 1px solid #dee2e6;
        }
        .file-list li:last-child {
            border-bottom: none;
        }
        .icon {
            color: #28a745;
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔬 Biomedical Electronics Virtual Lab</h1>
        
        <div class="status">
            <strong>✅ Website Status: WORKING</strong><br>
            If you can see this page, your web server is functioning correctly.
        </div>
        
        <h2>Quick Navigation Test</h2>
        <div style="text-align: center;">
            <a href="index.html" class="btn">🏠 Main Landing Page</a>
            <a href="curriculum.html" class="btn">📚 Curriculum Details</a>
            <a href="pricing.html" class="btn">💰 Pricing Plans</a>
        </div>
        
        <h2>File Structure Check</h2>
        <div class="file-list">
            <h3>Required Files:</h3>
            <ul>
                <li><span class="icon">✓</span> index.html - Main landing page</li>
                <li><span class="icon">✓</span> curriculum.html - Curriculum details</li>
                <li><span class="icon">✓</span> pricing.html - Pricing page</li>
                <li><span class="icon">✓</span> assets/css/styles.css - Main stylesheet</li>
                <li><span class="icon">✓</span> assets/js/script.js - JavaScript functionality</li>
                <li><span class="icon">✓</span> assets/js/analytics-tracking.js - Analytics code</li>
            </ul>
        </div>
        
        <h2>Troubleshooting</h2>
        <div style="background: #fff3cd; padding: 15px; border-radius: 5px; border: 1px solid #ffeaa7;">
            <h4>If the main pages don't load:</h4>
            <ol>
                <li>Check that all files are uploaded to the correct directory</li>
                <li>Verify file permissions (644 for files, 755 for directories)</li>
                <li>Ensure the CSS and JS files are in the assets/ folder</li>
                <li>Check browser console for any error messages</li>
                <li>Try refreshing the page or clearing browser cache</li>
            </ol>
        </div>
        
        <h2>Contact Information</h2>
        <div style="background: #e3f2fd; padding: 15px; border-radius: 5px; border: 1px solid #bbdefb;">
            <p><strong>Developer:</strong> Dr. Mohammed Yagoub Esmail, SUST - BME</p>
            <p><strong>Email:</strong> <EMAIL></p>
            <p><strong>Phone:</strong> +249 912 867 327 | +966 538 076 790</p>
        </div>
        
        <div style="text-align: center; margin-top: 30px;">
            <p><small>Test page created: <span id="datetime"></span></small></p>
        </div>
    </div>
    
    <script>
        // Display current date and time
        document.getElementById('datetime').textContent = new Date().toLocaleString();
        
        // Test JavaScript functionality
        console.log('✅ JavaScript is working correctly');
        
        // Test external resource loading
        if (document.fonts) {
            document.fonts.ready.then(function() {
                console.log('✅ Fonts loaded successfully');
            });
        }
    </script>
</body>
</html>
