
import React, { useState, useEffect, useCallback } from 'react';
import { ScenarioSelector } from './components/ScenarioSelector';
import { PatientMonitor } from './components/PatientMonitor';
import { DiagramGallery } from './components/DiagramGallery';
import { LoadingSpinner } from './components/LoadingSpinner';
import { ErrorDisplay } from './components/ErrorDisplay';
import { InteractiveDiagramsPage } from './components/InteractiveDiagramsPage'; // New Page
import { fetchVitalSignsData } from './services/geminiService';
import { DEFAULT_SCENARIO, SCENARIOS, APP_TITLE, DIAGRAMS, ECG_SYSTEM_COMPONENTS } from './constants';
import type { VitalSigns, ECGPoint } from './types';

type View = 'simulator' | 'diagramsPage';

const App: React.FC = () => {
  const [currentView, setCurrentView] = useState<View>('simulator');
  const [currentScenario, setCurrentScenario] = useState<string>(DEFAULT_SCENARIO);
  const [vitalSigns, setVitalSigns] = useState<VitalSigns | null>(null);
  const [ecgWaveform, setEcgWaveform] = useState<ECGPoint[]>([]);
  const [ecgDescription, setEcgDescription] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  const loadPatientData = useCallback(async (scenario: string) => {
    setIsLoading(true);
    setError(null);
    try {
      const data = await fetchVitalSignsData(scenario);
      setVitalSigns({
        heartRate: data.heartRate,
        spo2: data.spo2,
        systolicBP: data.systolicBP,
        diastolicBP: data.diastolicBP,
        respiratoryRate: data.respiratoryRate,
        temperature: data.temperature,
      });
      setEcgWaveform(data.ecgWaveform);
      setEcgDescription(data.ecgDescription);
    } catch (err) {
      console.error("Failed to load patient data:", err);
      setError(err instanceof Error ? err.message : 'An unknown error occurred.');
      setVitalSigns(null);
      setEcgWaveform([]);
      setEcgDescription('');
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    if (currentView === 'simulator') {
      loadPatientData(currentScenario);
    }
  }, [currentScenario, loadPatientData, currentView]);

  const handleScenarioChange = (scenario: string) => {
    setCurrentScenario(scenario);
  };

  const toggleView = () => {
    setCurrentView(prev => prev === 'simulator' ? 'diagramsPage' : 'simulator');
    // Reset error/loading when switching views if simulator specific
    if (currentView !== 'simulator') {
        setError(null); 
        // Potentially reload data if switching back to simulator and it hasn't loaded for the current scenario
        // For now, useEffect handles this.
    }
  };

  const renderSimulatorView = () => (
    <>
      <aside className="w-full lg:w-1/4 xl:w-1/5 space-y-6">
        <ScenarioSelector
          scenarios={SCENARIOS}
          selectedScenario={currentScenario}
          onScenarioChange={handleScenarioChange}
          isLoading={isLoading}
        />
        <DiagramGallery diagrams={DIAGRAMS} />
      </aside>

      <section className="w-full lg:w-3/4 xl:w-4/5">
        {isLoading && <div className="flex justify-center items-center h-full min-h-[400px]"><LoadingSpinner /></div>}
        {error && !isLoading && <ErrorDisplay message={error} onRetry={() => loadPatientData(currentScenario)} />}
        {!isLoading && !error && vitalSigns && (
          <PatientMonitor
            vitalSigns={vitalSigns}
            ecgWaveform={ecgWaveform}
            ecgDescription={ecgDescription}
          />
        )}
         {!isLoading && !error && !vitalSigns && (
           <div className="flex justify-center items-center h-full min-h-[400px] bg-white rounded-lg shadow-lg p-6">
              <p className="text-slate-500 text-lg">No patient data available. Please select a scenario or try again.</p>
           </div>
         )}
      </section>
    </>
  );

  return (
    <div className="min-h-screen bg-slate-100 text-slate-800 flex flex-col p-4 md:p-6">
      <header className="mb-6 flex flex-col sm:flex-row justify-between items-center">
        <h1 className="text-3xl md:text-4xl font-bold text-blue-700 mb-2 sm:mb-0">{APP_TITLE}</h1>
        <button
          onClick={toggleView}
          className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md shadow-md transition-colors duration-150 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-75"
          aria-label={currentView === 'simulator' ? "Switch to Interactive Diagrams" : "Switch to Patient Simulator"}
        >
          {currentView === 'simulator' ? 'ECG System Diagrams' : 'Back to Simulator'}
        </button>
      </header>

      <main className="flex-grow flex flex-col lg:flex-row gap-6">
        {currentView === 'simulator' ? renderSimulatorView() : <InteractiveDiagramsPage components={ECG_SYSTEM_COMPONENTS} />}
      </main>

      <footer className="mt-8 text-center text-sm text-slate-500">
        <p>&copy; {new Date().getFullYear()} Biomedical Simulator. For educational purposes only.</p>
        <p>Powered by React, Tailwind CSS, and Gemini API.</p>
      </footer>
    </div>
  );
};

export default App;
