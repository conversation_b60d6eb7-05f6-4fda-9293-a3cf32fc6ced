// Biomedical Electronics Virtual Lab - Analytics Tracking
// Comprehensive tracking implementation for user behavior and conversions

// Configuration - Replace with your actual IDs
const ANALYTICS_CONFIG = {
    GA4_MEASUREMENT_ID: 'G-XXXXXXXXXX', // Replace with your GA4 Measurement ID
    GTM_CONTAINER_ID: 'GTM-XXXXXXX',    // Replace with your GTM Container ID
    DEBUG_MODE: false                    // Set to true for testing
};

// Initialize Google Analytics 4
function initializeGA4() {
    // Load gtag script
    const script = document.createElement('script');
    script.async = true;
    script.src = `https://www.googletagmanager.com/gtag/js?id=${ANALYTICS_CONFIG.GA4_MEASUREMENT_ID}`;
    document.head.appendChild(script);

    // Initialize gtag
    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments);}
    gtag('js', new Date());

    // Configure GA4 with enhanced settings
    gtag('config', ANALYTICS_CONFIG.GA4_MEASUREMENT_ID, {
        // Enhanced measurement settings
        enhanced_measurement: {
            scrolls: true,
            outbound_clicks: true,
            site_search: true,
            video_engagement: true,
            file_downloads: true
        },
        // Custom parameters
        custom_map: {
            'custom_parameter_1': 'user_type',
            'custom_parameter_2': 'traffic_source',
            'custom_parameter_3': 'page_category'
        },
        // Privacy settings
        anonymize_ip: true,
        allow_google_signals: true,
        allow_ad_personalization_signals: false
    });

    // Make gtag globally available
    window.gtag = gtag;
}

// Initialize Google Tag Manager
function initializeGTM() {
    // GTM script
    (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
    new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
    j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
    'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
    })(window,document,'script','dataLayer',ANALYTICS_CONFIG.GTM_CONTAINER_ID);
}

// Core Event Tracking Functions
const AnalyticsTracker = {
    
    // Track demo requests (primary conversion)
    trackDemoRequest: function(userType = 'unknown', source = 'website') {
        if (typeof gtag !== 'undefined') {
            gtag('event', 'demo_request', {
                event_category: 'conversion',
                event_label: userType,
                value: 50, // Estimated lead value
                user_type: userType,
                traffic_source: source,
                currency: 'USD'
            });
        }
        
        // Also push to dataLayer for GTM
        window.dataLayer = window.dataLayer || [];
        window.dataLayer.push({
            event: 'demo_request',
            user_type: userType,
            traffic_source: source,
            conversion_value: 50
        });

        if (ANALYTICS_CONFIG.DEBUG_MODE) {
            console.log('Demo request tracked:', { userType, source });
        }
    },

    // Track email signups (secondary conversion)
    trackEmailSignup: function(listType = 'newsletter', source = 'website') {
        if (typeof gtag !== 'undefined') {
            gtag('event', 'email_signup', {
                event_category: 'lead_generation',
                event_label: listType,
                value: 10, // Estimated lead value
                list_type: listType,
                traffic_source: source
            });
        }

        window.dataLayer = window.dataLayer || [];
        window.dataLayer.push({
            event: 'email_signup',
            list_type: listType,
            traffic_source: source,
            conversion_value: 10
        });

        if (ANALYTICS_CONFIG.DEBUG_MODE) {
            console.log('Email signup tracked:', { listType, source });
        }
    },

    // Track video engagement
    trackVideoPlay: function(videoName, progress = 0, duration = 0) {
        if (typeof gtag !== 'undefined') {
            gtag('event', 'video_play', {
                event_category: 'engagement',
                event_label: videoName,
                value: progress,
                video_title: videoName,
                video_progress: progress,
                video_duration: duration
            });
        }

        window.dataLayer = window.dataLayer || [];
        window.dataLayer.push({
            event: 'video_play',
            video_title: videoName,
            video_progress: progress,
            video_duration: duration
        });

        if (ANALYTICS_CONFIG.DEBUG_MODE) {
            console.log('Video play tracked:', { videoName, progress, duration });
        }
    },

    // Track curriculum downloads
    trackCurriculumDownload: function(moduleNumber, moduleName) {
        if (typeof gtag !== 'undefined') {
            gtag('event', 'curriculum_download', {
                event_category: 'content',
                event_label: `module_${moduleNumber}`,
                value: 1,
                module_number: moduleNumber,
                module_name: moduleName
            });
        }

        window.dataLayer = window.dataLayer || [];
        window.dataLayer.push({
            event: 'curriculum_download',
            module_number: moduleNumber,
            module_name: moduleName
        });

        if (ANALYTICS_CONFIG.DEBUG_MODE) {
            console.log('Curriculum download tracked:', { moduleNumber, moduleName });
        }
    },

    // Track pricing page interactions
    trackPricingInteraction: function(planType, action = 'view') {
        if (typeof gtag !== 'undefined') {
            gtag('event', 'pricing_interaction', {
                event_category: 'purchase_intent',
                event_label: planType,
                value: 1,
                plan_type: planType,
                interaction_type: action
            });
        }

        window.dataLayer = window.dataLayer || [];
        window.dataLayer.push({
            event: 'pricing_interaction',
            plan_type: planType,
            interaction_type: action
        });

        if (ANALYTICS_CONFIG.DEBUG_MODE) {
            console.log('Pricing interaction tracked:', { planType, action });
        }
    },

    // Track form interactions
    trackFormInteraction: function(formName, action, success = null) {
        const eventName = `form_${action}`;
        
        if (typeof gtag !== 'undefined') {
            gtag('event', eventName, {
                event_category: 'form_interaction',
                event_label: formName,
                value: success !== null ? (success ? 1 : 0) : 1,
                form_name: formName,
                form_action: action,
                form_success: success
            });
        }

        window.dataLayer = window.dataLayer || [];
        window.dataLayer.push({
            event: eventName,
            form_name: formName,
            form_action: action,
            form_success: success
        });

        if (ANALYTICS_CONFIG.DEBUG_MODE) {
            console.log('Form interaction tracked:', { formName, action, success });
        }
    },

    // Track scroll depth
    trackScrollDepth: function(percentage) {
        if (typeof gtag !== 'undefined') {
            gtag('event', 'scroll_depth', {
                event_category: 'engagement',
                event_label: `${percentage}%`,
                value: percentage,
                scroll_depth: percentage
            });
        }

        window.dataLayer = window.dataLayer || [];
        window.dataLayer.push({
            event: 'scroll_depth',
            scroll_depth: percentage
        });

        if (ANALYTICS_CONFIG.DEBUG_MODE) {
            console.log('Scroll depth tracked:', percentage + '%');
        }
    },

    // Track page performance
    trackPagePerformance: function() {
        if ('performance' in window) {
            window.addEventListener('load', function() {
                setTimeout(function() {
                    const perfData = performance.getEntriesByType('navigation')[0];
                    const loadTime = perfData.loadEventEnd - perfData.loadEventStart;
                    const domContentLoaded = perfData.domContentLoadedEventEnd - perfData.domContentLoadedEventStart;

                    if (typeof gtag !== 'undefined') {
                        gtag('event', 'page_load_time', {
                            event_category: 'performance',
                            value: Math.round(loadTime),
                            load_time: Math.round(loadTime),
                            dom_content_loaded: Math.round(domContentLoaded),
                            page_url: window.location.pathname
                        });
                    }

                    window.dataLayer = window.dataLayer || [];
                    window.dataLayer.push({
                        event: 'page_performance',
                        load_time: Math.round(loadTime),
                        dom_content_loaded: Math.round(domContentLoaded),
                        page_url: window.location.pathname
                    });

                    if (ANALYTICS_CONFIG.DEBUG_MODE) {
                        console.log('Page performance tracked:', {
                            loadTime: Math.round(loadTime),
                            domContentLoaded: Math.round(domContentLoaded)
                        });
                    }
                }, 0);
            });
        }
    },

    // Set user properties
    setUserProperties: function(userType, institution = null, userRole = null) {
        if (typeof gtag !== 'undefined') {
            gtag('config', ANALYTICS_CONFIG.GA4_MEASUREMENT_ID, {
                custom_map: {
                    user_type: userType,
                    institution: institution,
                    user_role: userRole
                }
            });
        }

        window.dataLayer = window.dataLayer || [];
        window.dataLayer.push({
            event: 'user_properties_set',
            user_type: userType,
            institution: institution,
            user_role: userRole
        });

        if (ANALYTICS_CONFIG.DEBUG_MODE) {
            console.log('User properties set:', { userType, institution, userRole });
        }
    }
};

// Automatic Scroll Depth Tracking
function initializeScrollTracking() {
    const scrollDepths = [25, 50, 75, 90];
    const scrollDepthsReached = [];

    window.addEventListener('scroll', function() {
        const scrollPercent = Math.round((window.scrollY / (document.body.scrollHeight - window.innerHeight)) * 100);
        
        scrollDepths.forEach(function(depth) {
            if (scrollPercent >= depth && scrollDepthsReached.indexOf(depth) === -1) {
                scrollDepthsReached.push(depth);
                AnalyticsTracker.trackScrollDepth(depth);
            }
        });
    });
}

// Automatic Link Tracking
function initializeLinkTracking() {
    document.addEventListener('click', function(event) {
        const link = event.target.closest('a');
        if (link) {
            const href = link.getAttribute('href');
            const text = link.textContent.trim();
            
            // Track external links
            if (href && (href.startsWith('http') && !href.includes(window.location.hostname))) {
                if (typeof gtag !== 'undefined') {
                    gtag('event', 'click', {
                        event_category: 'outbound_link',
                        event_label: href,
                        link_text: text,
                        link_url: href
                    });
                }
            }
            
            // Track CTA button clicks
            if (link.classList.contains('btn') || link.classList.contains('cta-button')) {
                if (typeof gtag !== 'undefined') {
                    gtag('event', 'cta_click', {
                        event_category: 'engagement',
                        event_label: text,
                        button_text: text,
                        button_location: window.location.pathname
                    });
                }
            }
        }
    });
}

// Initialize all tracking
function initializeAnalytics() {
    // Initialize core analytics
    initializeGA4();
    initializeGTM();
    
    // Initialize automatic tracking
    initializeScrollTracking();
    initializeLinkTracking();
    AnalyticsTracker.trackPagePerformance();
    
    // Set page category based on URL
    const path = window.location.pathname;
    let pageCategory = 'other';
    if (path === '/' || path === '/index.html') pageCategory = 'landing';
    else if (path.includes('curriculum')) pageCategory = 'curriculum';
    else if (path.includes('pricing')) pageCategory = 'pricing';
    else if (path.includes('demo')) pageCategory = 'demo';
    
    // Set initial page properties
    if (typeof gtag !== 'undefined') {
        gtag('config', ANALYTICS_CONFIG.GA4_MEASUREMENT_ID, {
            page_category: pageCategory,
            page_location: window.location.href,
            page_title: document.title
        });
    }

    if (ANALYTICS_CONFIG.DEBUG_MODE) {
        console.log('Analytics initialized for page category:', pageCategory);
    }
}

// Export for global use
window.AnalyticsTracker = AnalyticsTracker;
window.initializeAnalytics = initializeAnalytics;

// Auto-initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeAnalytics);
} else {
    initializeAnalytics();
}
