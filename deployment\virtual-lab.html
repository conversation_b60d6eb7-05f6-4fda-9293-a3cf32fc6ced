<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Virtual Lab Platform - Biomedical Measurement Systems</title>
    <meta name="description" content="Interactive virtual laboratory for biomedical measurement systems. Hands-on experiments with ECG, EMG, EEG, blood pressure, and more.">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #ffffff;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Header */
        .header {
            background: #ffffff;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem 0;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: bold;
            color: #2563eb;
            text-decoration: none;
        }

        .nav-menu {
            display: flex;
            list-style: none;
            gap: 2rem;
        }

        .nav-link {
            color: #374151;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s;
        }

        .nav-link:hover {
            color: #2563eb;
        }

        .cta-nav {
            background: #2563eb;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            text-decoration: none;
        }

        /* Hero Section */
        .hero {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 4rem 0;
            text-align: center;
        }

        .hero h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
        }

        .hero p {
            font-size: 1.25rem;
            opacity: 0.95;
            max-width: 800px;
            margin: 0 auto 2rem;
        }

        .hero-features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 2rem;
        }

        .hero-feature {
            background: rgba(255,255,255,0.1);
            padding: 1rem;
            border-radius: 0.5rem;
            backdrop-filter: blur(10px);
        }

        /* Platform Features */
        .platform-features {
            padding: 4rem 0;
            background: #f9fafb;
        }

        .section-title {
            font-size: 2.5rem;
            text-align: center;
            margin-bottom: 3rem;
            color: #1f2937;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }

        .feature-card {
            background: white;
            padding: 2rem;
            border-radius: 1rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            border-left: 4px solid #2563eb;
            transition: transform 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        .feature-icon {
            font-size: 2rem;
            margin-bottom: 1rem;
        }

        .feature-card h3 {
            color: #2563eb;
            margin-bottom: 1rem;
        }

        /* Modules Grid */
        .modules-section {
            padding: 4rem 0;
        }

        .modules-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }

        .module-card {
            background: white;
            border-radius: 1rem;
            padding: 2rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            border: 2px solid #e5e7eb;
            transition: all 0.3s ease;
            position: relative;
        }

        .module-card:hover {
            border-color: #2563eb;
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        .module-header {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
        }

        .module-number {
            background: #2563eb;
            color: white;
            width: 3rem;
            height: 3rem;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 1rem;
            font-size: 0.9rem;
        }

        .module-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #1f2937;
        }

        .module-experiments {
            margin: 1rem 0;
        }

        .experiment-list {
            list-style: none;
            padding: 0;
        }

        .experiment-list li {
            padding: 0.5rem 0;
            padding-left: 1.5rem;
            position: relative;
            color: #374151;
            font-size: 0.9rem;
        }

        .experiment-list li::before {
            content: '🧪';
            position: absolute;
            left: 0;
        }

        .module-link {
            display: inline-block;
            background: #2563eb;
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 0.5rem;
            text-decoration: none;
            font-weight: 600;
            margin-top: 1rem;
            transition: background 0.3s;
        }

        .module-link:hover {
            background: #1d4ed8;
        }

        /* Interactive Demo */
        .demo-section {
            padding: 4rem 0;
            background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
            color: white;
            text-align: center;
        }

        .demo-preview {
            background: rgba(255,255,255,0.1);
            padding: 2rem;
            border-radius: 1rem;
            margin: 2rem 0;
            backdrop-filter: blur(10px);
        }

        .btn {
            display: inline-block;
            padding: 1rem 2rem;
            border-radius: 0.5rem;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            margin: 0.5rem;
        }

        .btn-primary {
            background: #2563eb;
            color: white;
        }

        .btn-primary:hover {
            background: #1d4ed8;
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: transparent;
            border: 2px solid white;
            color: white;
        }

        .btn-secondary:hover {
            background: white;
            color: #1f2937;
        }

        /* Footer */
        .footer {
            background: #1f2937;
            color: white;
            padding: 3rem 0 1rem;
            text-align: center;
        }

        .footer p {
            margin: 0.5rem 0;
            opacity: 0.8;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .nav-menu {
                display: none;
            }

            .hero h1 {
                font-size: 2rem;
            }

            .hero-features {
                grid-template-columns: 1fr;
            }

            .modules-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="nav container">
            <a href="index.html" class="logo">🔬 BioMed Virtual Lab</a>
            <ul class="nav-menu">
                <li><a href="index.html" class="nav-link">Home</a></li>
                <li><a href="curriculum.html" class="nav-link">Curriculum</a></li>
                <li><a href="virtual-lab.html" class="nav-link">Virtual Lab</a></li>
                <li><a href="pricing.html" class="nav-link">Pricing</a></li>
                <li><a href="mailto:<EMAIL>?subject=Demo Request" class="cta-nav">Request Demo</a></li>
            </ul>
        </nav>
    </header>

    <!-- Hero -->
    <section class="hero">
        <div class="container">
            <h1>Interactive Virtual Laboratory</h1>
            <p>Experience hands-on biomedical measurement systems through our comprehensive virtual lab platform. Build circuits, use instruments, and perform real experiments - all in your browser.</p>
            
            <div class="hero-features">
                <div class="hero-feature">
                    <h4>🔧 Virtual Breadboard</h4>
                    <p>Drag & drop components</p>
                </div>
                <div class="hero-feature">
                    <h4>📊 Real Instruments</h4>
                    <p>Oscilloscope, multimeter, function generator</p>
                </div>
                <div class="hero-feature">
                    <h4>🧪 Guided Experiments</h4>
                    <p>Step-by-step procedures</p>
                </div>
                <div class="hero-feature">
                    <h4>📈 Data Analysis</h4>
                    <p>Plot & analyze results</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Platform Features -->
    <section class="platform-features">
        <div class="container">
            <h2 class="section-title">Virtual Lab Platform Features</h2>
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">🔧</div>
                    <h3>Virtual Breadboard & Wiring</h3>
                    <p>Interactive breadboard interface where users can place components and draw connections. Real-time circuit validation and visual feedback.</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">📦</div>
                    <h3>Component Library</h3>
                    <p>Comprehensive library including resistors, capacitors, op-amps, sensors, microcontrollers, and specialized biomedical transducers.</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">📊</div>
                    <h3>Virtual Instruments</h3>
                    <p>Professional-grade virtual oscilloscope, function generator, multimeter, power supply, and spectrum analyzer with realistic interfaces.</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">📋</div>
                    <h3>Schematic Viewer</h3>
                    <p>Automatic schematic generation from breadboard layout. Switch between breadboard and schematic views seamlessly.</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">📈</div>
                    <h3>Data Analysis Tools</h3>
                    <p>Built-in plotting and analysis tools for waveforms, frequency responses, and measurement data with export capabilities.</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">🎯</div>
                    <h3>Guided Experiments</h3>
                    <p>Step-by-step instructions with objectives, procedures, expected outcomes, and interactive quizzes for learning reinforcement.</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">⚠️</div>
                    <h3>Fault Insertion</h3>
                    <p>Intelligent fault injection system for troubleshooting exercises. Learn to diagnose and fix real-world circuit problems.</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">💓</div>
                    <h3>Simulated Bio-Signals</h3>
                    <p>Realistic ECG, EMG, EEG, PPG signals with noise and artifacts. Programmable signal generators for various physiological conditions.</p>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">💻</div>
                    <h3>Microcontroller Programming</h3>
                    <p>Simplified block-based and code editor interfaces for virtual microcontroller programming with real-time simulation.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Modules Section -->
    <section class="modules-section">
        <div class="container">
            <h2 class="section-title">Biomedical Measurement System Modules</h2>
            <p style="text-align: center; font-size: 1.1rem; color: #6b7280; margin-bottom: 2rem;">
                12 comprehensive modules covering essential biomedical measurement systems with hands-on virtual experiments
            </p>
            
            <div class="modules-grid">
                <!-- ECG Module -->
                <div class="module-card">
                    <div class="module-header">
                        <div class="module-number">01</div>
                        <div class="module-title">Electrocardiogram (ECG)</div>
                    </div>
                    <p style="color: #6b7280; margin-bottom: 1rem;">Complete ECG signal acquisition and processing system</p>
                    <div class="module-experiments">
                        <ul class="experiment-list">
                            <li>HPF Characteristic Analysis</li>
                            <li>Amplifier Design & Testing</li>
                            <li>LPF Characteristic Analysis</li>
                            <li>Band-Reject Filter (Notch)</li>
                            <li>ECG Signal Processing Chain</li>
                            <li>Real ECG Measurement</li>
                        </ul>
                    </div>
                    <a href="ecg-module.html" class="module-link">Start ECG Module</a>
                </div>

                <!-- EMG Module -->
                <div class="module-card">
                    <div class="module-header">
                        <div class="module-number">02</div>
                        <div class="module-title">Electromyogram (EMG)</div>
                    </div>
                    <p style="color: #6b7280; margin-bottom: 1rem;">Muscle activity measurement and signal processing</p>
                    <div class="module-experiments">
                        <ul class="experiment-list">
                            <li>Signal Filtering & Amplification</li>
                            <li>Half-Wave Rectifier</li>
                            <li>Signal Integration</li>
                            <li>EMG Processing Chain</li>
                            <li>Muscle Activity Analysis</li>
                        </ul>
                    </div>
                    <a href="emg-module.html" class="module-link">Start EMG Module</a>
                </div>

                <!-- EOG Module -->
                <div class="module-card">
                    <div class="module-header">
                        <div class="module-number">03</div>
                        <div class="module-title">Electrooculogram (EOG)</div>
                    </div>
                    <p style="color: #6b7280; margin-bottom: 1rem;">Eye movement detection and measurement</p>
                    <div class="module-experiments">
                        <ul class="experiment-list">
                            <li>Horizontal & Vertical Calibration</li>
                            <li>Signal Conditioning</li>
                            <li>Eye Movement Detection</li>
                            <li>Calibration Procedures</li>
                        </ul>
                    </div>
                    <a href="eog-module.html" class="module-link">Start EOG Module</a>
                </div>

                <!-- EEG Module -->
                <div class="module-card">
                    <div class="module-header">
                        <div class="module-number">04</div>
                        <div class="module-title">Electroencephalogram (EEG)</div>
                    </div>
                    <p style="color: #6b7280; margin-bottom: 1rem;">Brain activity measurement and analysis</p>
                    <div class="module-experiments">
                        <ul class="experiment-list">
                            <li>Pre-Amplifier Calibration</li>
                            <li>Multi-stage Filtering</li>
                            <li>Brainwave Pattern Analysis</li>
                            <li>EEG Signal Processing</li>
                        </ul>
                    </div>
                    <a href="eeg-module.html" class="module-link">Start EEG Module</a>
                </div>

                <!-- Blood Pressure Module -->
                <div class="module-card">
                    <div class="module-header">
                        <div class="module-number">05</div>
                        <div class="module-title">Blood Pressure Measurement</div>
                    </div>
                    <p style="color: #6b7280; margin-bottom: 1rem;">Auscultatory and oscillometric methods</p>
                    <div class="module-experiments">
                        <ul class="experiment-list">
                            <li>Pressure Sensor Calibration</li>
                            <li>Korotkoff Sound Detection</li>
                            <li>Oscillometric Method</li>
                            <li>Signal Processing Chain</li>
                        </ul>
                    </div>
                    <a href="blood-pressure-module.html" class="module-link">Start BP Module</a>
                </div>

                <!-- PPG Module -->
                <div class="module-card">
                    <div class="module-header">
                        <div class="module-number">06</div>
                        <div class="module-title">Photoplethysmogram (PPG)</div>
                    </div>
                    <p style="color: #6b7280; margin-bottom: 1rem;">Optical pulse detection and analysis</p>
                    <div class="module-experiments">
                        <ul class="experiment-list">
                            <li>Infrared Photocoupler Setup</li>
                            <li>4th-order LPF Design</li>
                            <li>Pulse Detection Circuits</li>
                            <li>Heart Rate Calculation</li>
                        </ul>
                    </div>
                    <a href="ppg-module.html" class="module-link">Start PPG Module</a>
                </div>

                <!-- Respiratory Module -->
                <div class="module-card">
                    <div class="module-header">
                        <div class="module-number">07</div>
                        <div class="module-title">Respiratory Ventilation</div>
                    </div>
                    <p style="color: #6b7280; margin-bottom: 1rem;">Breathing pattern detection and analysis</p>
                    <div class="module-experiments">
                        <ul class="experiment-list">
                            <li>Differential Amplifier Setup</li>
                            <li>Hysteresis Comparator</li>
                            <li>Breath Detection Logic</li>
                            <li>Respiratory Rate Measurement</li>
                        </ul>
                    </div>
                    <a href="respiratory-module.html" class="module-link">Start Respiratory Module</a>
                </div>

                <!-- Pulse Meter Module -->
                <div class="module-card">
                    <div class="module-header">
                        <div class="module-number">08</div>
                        <div class="module-title">Pulse Meter</div>
                    </div>
                    <p style="color: #6b7280; margin-bottom: 1rem;">Mechanical pulse detection using strain gauge</p>
                    <div class="module-experiments">
                        <ul class="experiment-list">
                            <li>Strain Gauge Amplifier</li>
                            <li>Wheatstone Bridge Setup</li>
                            <li>Pulse Wave Analysis</li>
                            <li>Arterial Vessel Simulation</li>
                        </ul>
                    </div>
                    <a href="pulse-meter-module.html" class="module-link">Start Pulse Module</a>
                </div>

                <!-- Impedance Module -->
                <div class="module-card">
                    <div class="module-header">
                        <div class="module-number">09</div>
                        <div class="module-title">Bioimpedance Measurement</div>
                    </div>
                    <p style="color: #6b7280; margin-bottom: 1rem;">Tissue impedance measurement and analysis</p>
                    <div class="module-experiments">
                        <ul class="experiment-list">
                            <li>Wien-Bridge Oscillator</li>
                            <li>Demodulation Circuits</li>
                            <li>Impedance Detection</li>
                            <li>Tissue Modeling</li>
                        </ul>
                    </div>
                    <a href="impedance-module.html" class="module-link">Start Impedance Module</a>
                </div>

                <!-- Doppler Module -->
                <div class="module-card">
                    <div class="module-header">
                        <div class="module-number">10</div>
                        <div class="module-title">Doppler Ultrasound</div>
                    </div>
                    <p style="color: #6b7280; margin-bottom: 1rem;">Blood velocity measurement using Doppler effect</p>
                    <div class="module-experiments">
                        <ul class="experiment-list">
                            <li>Ultrasound Oscillator</li>
                            <li>Doppler Demodulation</li>
                            <li>Velocity Calculation</li>
                            <li>Signal Processing Chain</li>
                        </ul>
                    </div>
                    <a href="doppler-module.html" class="module-link">Start Doppler Module</a>
                </div>

                <!-- TENS Module -->
                <div class="module-card">
                    <div class="module-header">
                        <div class="module-number">11</div>
                        <div class="module-title">TENS Unit</div>
                    </div>
                    <p style="color: #6b7280; margin-bottom: 1rem;">Transcutaneous electrical nerve stimulation</p>
                    <div class="module-experiments">
                        <ul class="experiment-list">
                            <li>555 Timer Astable Mode</li>
                            <li>Transistor Switch Circuits</li>
                            <li>Pulse Generation & Control</li>
                            <li>Safety Considerations</li>
                        </ul>
                    </div>
                    <a href="tens-module.html" class="module-link">Start TENS Module</a>
                </div>

                <!-- Spirometry Module -->
                <div class="module-card">
                    <div class="module-header">
                        <div class="module-number">12</div>
                        <div class="module-title">Spirometry / Vital Capacity</div>
                    </div>
                    <p style="color: #6b7280; margin-bottom: 1rem;">Lung function and respiratory flow measurement</p>
                    <div class="module-experiments">
                        <ul class="experiment-list">
                            <li>Hall Effect Sensor Interface</li>
                            <li>Frequency-to-Voltage Conversion</li>
                            <li>Digital Display Systems</li>
                            <li>Flow & Volume Calculation</li>
                        </ul>
                    </div>
                    <a href="spirometry-module.html" class="module-link">Start Spirometry Module</a>
                </div>
            </div>
        </div>
    </section>

    <!-- Interactive Demo -->
    <section class="demo-section">
        <div class="container">
            <h2>Experience the Virtual Lab</h2>
            <p style="margin-bottom: 2rem;">See how our interactive platform brings biomedical electronics to life</p>
            
            <div class="demo-preview">
                <h3>🔬 Virtual Breadboard Interface</h3>
                <p>Drag components, make connections, and build real circuits in our intuitive virtual environment. Watch your circuits come to life with realistic simulations.</p>
            </div>
            
            <div style="margin: 2rem 0;">
                <a href="mailto:<EMAIL>?subject=Virtual Lab Demo Request" class="btn btn-primary">Request Live Demo</a>
                <a href="test-website.html" class="btn btn-secondary">Test Platform</a>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <h3>Biomedical Electronics Virtual Lab</h3>
            <p>Developed by Dr. Mohammed Yagoub Esmail, SUST - BME</p>
            <p>📧 Email: <EMAIL></p>
            <p>📞 Phone: +249 912 867 327 | +966 538 076 790</p>
            <p>&copy; 2025. All Rights Reserved.</p>
        </div>
    </footer>

    <script>
        // Smooth scrolling
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        console.log('✅ Virtual Lab platform page loaded successfully!');
    </script>
</body>
</html>
