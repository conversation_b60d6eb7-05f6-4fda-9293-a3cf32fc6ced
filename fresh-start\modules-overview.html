<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interactive Modules Overview - Biomedical Measurement Systems</title>
    <meta name="description" content="Comprehensive overview of all 12 interactive biomedical measurement system modules with detailed experiment descriptions.">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #ffffff;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Header */
        .header {
            background: #ffffff;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem 0;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: bold;
            color: #2563eb;
            text-decoration: none;
        }

        .nav-menu {
            display: flex;
            list-style: none;
            gap: 2rem;
        }

        .nav-link {
            color: #374151;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s;
        }

        .nav-link:hover {
            color: #2563eb;
        }

        .cta-nav {
            background: #2563eb;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            text-decoration: none;
        }

        /* Hero Section */
        .hero {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 4rem 0;
            text-align: center;
        }

        .hero h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
        }

        .hero p {
            font-size: 1.25rem;
            opacity: 0.95;
            max-width: 800px;
            margin: 0 auto;
        }

        /* Module Sections */
        .modules-section {
            padding: 4rem 0;
        }

        .module-card {
            background: white;
            border-radius: 1rem;
            padding: 3rem;
            margin-bottom: 3rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            border-left: 6px solid #2563eb;
        }

        .module-header {
            display: flex;
            align-items: center;
            margin-bottom: 2rem;
        }

        .module-number {
            background: #2563eb;
            color: white;
            width: 4rem;
            height: 4rem;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 2rem;
            font-size: 1.2rem;
        }

        .module-title {
            font-size: 2rem;
            font-weight: 600;
            color: #1f2937;
        }

        .module-description {
            color: #6b7280;
            font-size: 1.1rem;
            margin-bottom: 2rem;
        }

        .experiments-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }

        .experiment-card {
            background: #f8fafc;
            padding: 2rem;
            border-radius: 0.5rem;
            border-left: 4px solid #10b981;
        }

        .experiment-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 1rem;
        }

        .experiment-activities {
            margin: 1rem 0;
        }

        .activity-section {
            margin: 1rem 0;
        }

        .activity-section h5 {
            color: #2563eb;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .activity-list {
            list-style: none;
            padding-left: 1rem;
        }

        .activity-list li {
            padding: 0.25rem 0;
            position: relative;
        }

        .activity-list li::before {
            content: '▶';
            color: #10b981;
            position: absolute;
            left: -1rem;
        }

        .learning-objectives {
            background: #eff6ff;
            padding: 1.5rem;
            border-radius: 0.5rem;
            border-left: 4px solid #3b82f6;
            margin: 2rem 0;
        }

        .platform-features {
            background: #f0fdf4;
            padding: 1.5rem;
            border-radius: 0.5rem;
            border-left: 4px solid #22c55e;
            margin: 2rem 0;
        }

        .btn {
            display: inline-block;
            padding: 1rem 2rem;
            border-radius: 0.5rem;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            margin: 0.5rem;
        }

        .btn-primary {
            background: #2563eb;
            color: white;
        }

        .btn-primary:hover {
            background: #1d4ed8;
            transform: translateY(-2px);
        }

        .section-title {
            font-size: 2.5rem;
            text-align: center;
            margin-bottom: 3rem;
            color: #1f2937;
        }

        /* Footer */
        .footer {
            background: #1f2937;
            color: white;
            padding: 3rem 0 1rem;
            text-align: center;
        }

        .footer p {
            margin: 0.5rem 0;
            opacity: 0.8;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .nav-menu {
                display: none;
            }

            .hero h1 {
                font-size: 2rem;
            }

            .experiments-grid {
                grid-template-columns: 1fr;
            }

            .module-header {
                flex-direction: column;
                text-align: center;
            }

            .module-number {
                margin-right: 0;
                margin-bottom: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="nav container">
            <a href="index.html" class="logo">🔬 BioMed Virtual Lab</a>
            <ul class="nav-menu">
                <li><a href="index.html" class="nav-link">Home</a></li>
                <li><a href="virtual-lab.html" class="nav-link">Virtual Lab</a></li>
                <li><a href="curriculum.html" class="nav-link">Curriculum</a></li>
                <li><a href="pricing.html" class="nav-link">Pricing</a></li>
                <li><a href="mailto:<EMAIL>?subject=Demo Request" class="cta-nav">Request Demo</a></li>
            </ul>
        </nav>
    </header>

    <!-- Hero -->
    <section class="hero">
        <div class="container">
            <h1>Interactive Virtual Training Modules</h1>
            <p>Comprehensive hands-on experiments in biomedical measurement systems. Each module provides guided learning with virtual breadboards, instruments, and real-time analysis tools.</p>
        </div>
    </section>

    <!-- Platform Features Overview -->
    <section class="modules-section">
        <div class="container">
            <div class="platform-features">
                <h3>🔬 Virtual Lab Platform Capabilities</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1rem; margin-top: 1rem;">
                    <div>
                        <strong>🔧 Virtual Breadboard:</strong> Drag & drop components, real-time wiring interface
                    </div>
                    <div>
                        <strong>📦 Component Library:</strong> Resistors, capacitors, op-amps, sensors, microcontrollers
                    </div>
                    <div>
                        <strong>📊 Virtual Instruments:</strong> Oscilloscope, multimeter, function generator, spectrum analyzer
                    </div>
                    <div>
                        <strong>📋 Schematic Viewer:</strong> Automatic circuit diagram generation
                    </div>
                    <div>
                        <strong>📈 Data Analysis:</strong> Real-time plotting, frequency response, measurement tools
                    </div>
                    <div>
                        <strong>🎯 Guided Learning:</strong> Step-by-step procedures, quizzes, interactive feedback
                    </div>
                    <div>
                        <strong>⚠️ Fault Insertion:</strong> Troubleshooting exercises with intelligent fault injection
                    </div>
                    <div>
                        <strong>💓 Bio-Signal Simulation:</strong> Realistic ECG, EMG, EEG, PPG signals with noise
                    </div>
                </div>
            </div>

            <h2 class="section-title">Complete Module Breakdown</h2>

            <!-- ECG Module -->
            <div class="module-card">
                <div class="module-header">
                    <div class="module-number">01</div>
                    <div>
                        <div class="module-title">💓 Electrocardiogram (ECG) Module</div>
                        <div class="module-description">Complete ECG signal acquisition and processing system with filtering and amplification</div>
                    </div>
                </div>

                <div class="experiments-grid">
                    <div class="experiment-card">
                        <div class="experiment-title">🧪 HPF Characteristic Experiment</div>
                        <div class="experiment-activities">
                            <div class="activity-section">
                                <h5>Circuit Building:</h5>
                                <ul class="activity-list">
                                    <li>Place op-amp (LM741) on virtual breadboard</li>
                                    <li>Connect feedback resistor (10kΩ) and input capacitor (1µF)</li>
                                    <li>Wire power supply connections (±15V)</li>
                                    <li>Add input/output connection points</li>
                                </ul>
                            </div>
                            <div class="activity-section">
                                <h5>Instrument Usage:</h5>
                                <ul class="activity-list">
                                    <li>Connect function generator to circuit input</li>
                                    <li>Configure oscilloscope for dual-channel measurement</li>
                                    <li>Set up spectrum analyzer for frequency response</li>
                                </ul>
                            </div>
                            <div class="activity-section">
                                <h5>Procedure:</h5>
                                <ul class="activity-list">
                                    <li>Sweep frequency from 0.01Hz to 100Hz</li>
                                    <li>Measure output amplitude at each frequency</li>
                                    <li>Calculate gain in dB and plot frequency response</li>
                                    <li>Identify cutoff frequency (-3dB point)</li>
                                </ul>
                            </div>
                            <div class="activity-section">
                                <h5>Analysis:</h5>
                                <ul class="activity-list">
                                    <li>Compare measured vs theoretical cutoff frequency</li>
                                    <li>Verify 20dB/decade roll-off rate</li>
                                    <li>Answer guided questions about filter behavior</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="experiment-card">
                        <div class="experiment-title">🧪 Complete ECG Experiment</div>
                        <div class="experiment-activities">
                            <div class="activity-section">
                                <h5>Circuit Building:</h5>
                                <ul class="activity-list">
                                    <li>Build instrumentation amplifier (3 op-amps)</li>
                                    <li>Add HPF (0.05Hz), LPF (100Hz), and notch filter (60Hz)</li>
                                    <li>Connect virtual ECG electrodes with simulated signals</li>
                                </ul>
                            </div>
                            <div class="activity-section">
                                <h5>Signal Processing:</h5>
                                <ul class="activity-list">
                                    <li>Process realistic ECG signals with noise and artifacts</li>
                                    <li>Observe filtered waveforms on oscilloscope</li>
                                    <li>Measure P-wave, QRS complex, and T-wave intervals</li>
                                    <li>Calculate heart rate from R-R intervals</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="learning-objectives">
                    <h4>🎯 Learning Objectives:</h4>
                    <ul style="margin-left: 2rem; margin-top: 0.5rem;">
                        <li>Understand ECG signal characteristics and frequency content</li>
                        <li>Design and implement active filters for biomedical applications</li>
                        <li>Master instrumentation amplifier design for high CMRR</li>
                        <li>Learn to process real physiological signals with noise rejection</li>
                    </ul>
                </div>
            </div>

            <!-- EMG Module -->
            <div class="module-card">
                <div class="module-header">
                    <div class="module-number">02</div>
                    <div>
                        <div class="module-title">💪 Electromyogram (EMG) Module</div>
                        <div class="module-description">Muscle activity measurement with signal rectification and integration</div>
                    </div>
                </div>

                <div class="experiments-grid">
                    <div class="experiment-card">
                        <div class="experiment-title">🧪 Half-Wave Rectifier Experiment</div>
                        <div class="experiment-activities">
                            <div class="activity-section">
                                <h5>Circuit Building:</h5>
                                <ul class="activity-list">
                                    <li>Place diode (1N4148) on breadboard</li>
                                    <li>Add load resistor and input coupling capacitor</li>
                                    <li>Connect AC signal source and oscilloscope probes</li>
                                </ul>
                            </div>
                            <div class="activity-section">
                                <h5>Testing:</h5>
                                <ul class="activity-list">
                                    <li>Apply sine wave from function generator</li>
                                    <li>Observe input and output waveforms simultaneously</li>
                                    <li>Measure forward voltage drop and efficiency</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="experiment-card">
                        <div class="experiment-title">🧪 EMG Processing Chain</div>
                        <div class="experiment-activities">
                            <div class="activity-section">
                                <h5>Complete System:</h5>
                                <ul class="activity-list">
                                    <li>Build amplifier → rectifier → integrator chain</li>
                                    <li>Connect virtual EMG electrodes with muscle activity simulation</li>
                                    <li>Process EMG signals to extract muscle activation envelope</li>
                                    <li>Correlate processed signal with simulated muscle force</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Blood Pressure Module -->
            <div class="module-card">
                <div class="module-header">
                    <div class="module-number">05</div>
                    <div>
                        <div class="module-title">🩸 Blood Pressure Measurement Module</div>
                        <div class="module-description">Auscultatory and oscillometric blood pressure measurement methods</div>
                    </div>
                </div>

                <div class="experiments-grid">
                    <div class="experiment-card">
                        <div class="experiment-title">🧪 Pressure Sensor Calibration</div>
                        <div class="experiment-activities">
                            <div class="activity-section">
                                <h5>Sensor Interface:</h5>
                                <ul class="activity-list">
                                    <li>Connect virtual pressure sensor (voltage output proportional to pressure)</li>
                                    <li>Build signal conditioning amplifier with adjustable gain</li>
                                    <li>Apply simulated pressure inputs (0-300 mmHg)</li>
                                    <li>Calibrate system for accurate pressure readings</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="experiment-card">
                        <div class="experiment-title">🧪 Oscillometric Method</div>
                        <div class="experiment-activities">
                            <div class="activity-section">
                                <h5>Signal Processing:</h5>
                                <ul class="activity-list">
                                    <li>Simulate cuff pressure oscillations during deflation</li>
                                    <li>Filter and amplify oscillation signals</li>
                                    <li>Detect systolic/diastolic points from oscillation envelope</li>
                                    <li>Calculate mean arterial pressure (MAP)</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- PPG Module -->
            <div class="module-card">
                <div class="module-header">
                    <div class="module-number">06</div>
                    <div>
                        <div class="module-title">🔴 Photoplethysmogram (PPG) Module</div>
                        <div class="module-description">Optical pulse detection and heart rate measurement</div>
                    </div>
                </div>

                <div class="experiments-grid">
                    <div class="experiment-card">
                        <div class="experiment-title">🧪 Infrared Photocoupler Setup</div>
                        <div class="experiment-activities">
                            <div class="activity-section">
                                <h5>Optical System:</h5>
                                <ul class="activity-list">
                                    <li>Configure virtual LED and photodiode pair</li>
                                    <li>Build transimpedance amplifier for photodiode</li>
                                    <li>Simulate light absorption changes due to blood volume</li>
                                    <li>Calibrate system sensitivity and dynamic range</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="experiment-card">
                        <div class="experiment-title">🧪 Pulse Detection Circuit</div>
                        <div class="experiment-activities">
                            <div class="activity-section">
                                <h5>Signal Processing:</h5>
                                <ul class="activity-list">
                                    <li>Build 4th-order LPF for noise reduction</li>
                                    <li>Add differentiator to detect pulse edges</li>
                                    <li>Implement comparator with hysteresis for pulse detection</li>
                                    <li>Use monostable multivibrator for pulse counting</li>
                                    <li>Calculate heart rate from pulse intervals</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- TENS Module -->
            <div class="module-card">
                <div class="module-header">
                    <div class="module-number">11</div>
                    <div>
                        <div class="module-title">⚡ TENS Module</div>
                        <div class="module-description">Transcutaneous Electrical Nerve Stimulation unit design</div>
                    </div>
                </div>

                <div class="experiments-grid">
                    <div class="experiment-card">
                        <div class="experiment-title">🧪 555 Timer Astable Mode</div>
                        <div class="experiment-activities">
                            <div class="activity-section">
                                <h5>Pulse Generator:</h5>
                                <ul class="activity-list">
                                    <li>Build 555 timer in astable configuration</li>
                                    <li>Calculate and set frequency (1-100 Hz)</li>
                                    <li>Adjust duty cycle using timing resistors</li>
                                    <li>Observe pulse train on oscilloscope</li>
                                    <li>Measure frequency stability and accuracy</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="experiment-card">
                        <div class="experiment-title">🧪 Transistor Switch Circuit</div>
                        <div class="experiment-activities">
                            <div class="activity-section">
                                <h5>Power Control:</h5>
                                <ul class="activity-list">
                                    <li>Design transistor switch for electrode drive</li>
                                    <li>Calculate base current and power dissipation</li>
                                    <li>Add current limiting and safety features</li>
                                    <li>Test with simulated electrode load</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Spirometry Module -->
            <div class="module-card">
                <div class="module-header">
                    <div class="module-number">12</div>
                    <div>
                        <div class="module-title">🫁 Spirometry / Vital Capacity Module</div>
                        <div class="module-description">Respiratory flow measurement and lung function analysis</div>
                    </div>
                </div>

                <div class="experiments-grid">
                    <div class="experiment-card">
                        <div class="experiment-title">🧪 Hall Effect Sensor Interface</div>
                        <div class="experiment-activities">
                            <div class="activity-section">
                                <h5>Flow Sensing:</h5>
                                <ul class="activity-list">
                                    <li>Configure Hall sensor for airflow measurement</li>
                                    <li>Build differential amplifier for sensor output</li>
                                    <li>Calibrate flow rate vs. sensor output</li>
                                    <li>Implement temperature compensation</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="experiment-card">
                        <div class="experiment-title">🧪 Digital Display System</div>
                        <div class="experiment-activities">
                            <div class="activity-section">
                                <h5>Data Processing:</h5>
                                <ul class="activity-list">
                                    <li>Build frequency-to-voltage converter</li>
                                    <li>Implement decade counter for volume integration</li>
                                    <li>Add BCD-to-7-segment decoder</li>
                                    <li>Display flow rate and vital capacity measurements</li>
                                    <li>Calculate respiratory parameters (FEV1, FVC, etc.)</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div style="text-align: center; margin: 3rem 0;">
                <a href="virtual-lab.html" class="btn btn-primary">🚀 Start Virtual Lab Experience</a>
                <a href="mailto:<EMAIL>?subject=Modules Demo Request" class="btn btn-primary">📧 Request Demo</a>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <h3>Biomedical Electronics Virtual Lab</h3>
            <p>Developed by Dr. Mohammed Yagoub Esmail, SUST - BME</p>
            <p>📧 Email: <EMAIL></p>
            <p>📞 Phone: +249 912 867 327 | +966 538 076 790</p>
            <p>&copy; 2025. All Rights Reserved.</p>
        </div>
    </footer>

    <script>
        console.log('✅ Interactive Modules Overview loaded successfully!');
    </script>
</body>
</html>
