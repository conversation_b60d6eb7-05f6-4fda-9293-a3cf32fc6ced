import React from 'react'; // Import React for JSX and React.createElement
import { PatientScenario, ECGSystemComponentDetail } from './types';

export const APP_TITLE = "Biomedical Device Simulator Lab";

export const DEFAULT_SCENARIO: PatientScenario = PatientScenario.NORMAL;

export const SCENARIOS: string[] = Object.values(PatientScenario);

export interface DiagramInfo {
  id: string;
  imageUrl: string;
  title: string;
  description: string;
}

export const DIAGRAMS: DiagramInfo[] = [
  {
    id: 'diag1',
    imageUrl: 'https://i.imgur.com/Yq7XH1M.png',
    title: 'Remote Patient Monitoring System',
    description: 'Overall architecture of a remote patient monitoring setup, from patient to healthcare provider.',
  },
  {
    id: 'diag2',
    imageUrl: 'https://i.imgur.com/Qk9vj7M.png',
    title: 'Real-Time System Block Diagram',
    description: 'Illustrates sensors, signal processing, central processing, memory, and the real-time dissemination system.',
  },
  {
    id: 'diag3',
    imageUrl: 'https://i.imgur.com/xL2cCoH.png',
    title: 'Arduino Data Acquisition',
    description: 'Block diagram for data acquisition using Arduino: sensors, conditioning, ADC, Arduino, computer, and display.',
  },
  {
    id: 'diag4',
    imageUrl: 'https://i.imgur.com/uF2F20N.png',
    title: 'Digital Filter',
    description: 'Signal flow in a digital filter: A/D conversion, processing with filter algorithm, and D/A conversion.',
  },
  {
    id: 'diag5',
    imageUrl: 'https://i.imgur.com/3wXfT7B.png',
    title: 'Measurement Amplifier',
    description: 'Components of a measurement amplifier including differential input, gain control, and output stage.',
  },
  {
    id: 'diag6',
    imageUrl: 'https://i.imgur.com/DslR6hb.png',
    title: 'Driven Right Leg Circuit',
    description: 'Circuit diagram of a Driven Right Leg (DRL) system used to reduce common-mode interference in ECGs.',
  },
];

export const ECG_SYSTEM_COMPONENTS: ECGSystemComponentDetail[] = [
  {
    id: 'ecg_overall_realtime',
    title: 'Real-Time ECG System Overview',
    imageUrl: 'https://i.imgur.com/Qk9vj7M.png', // Re-using diag2
    overview: 'This block diagram shows the major functional units in a real-time ECG monitoring system. It starts from sensor data acquisition, proceeds through signal processing and analysis, and culminates in data display and storage.',
    type: 'Block Diagram',
    keyAspects: [
      { name: 'Sensors', description: 'Detect physiological signals (e.g., ECG via electrodes). Accuracy and reliability are paramount.'},
      { name: 'Signal Conditioning & Preprocessing', description: 'Includes amplification, filtering, and other operations to prepare the raw signal for digitization and analysis. Aims to improve signal-to-noise ratio (SNR).'},
      { name: 'Analog-to-Digital Converter (ADC)', description: 'Converts the analog sensor signal into a digital representation for processing by the central unit.'},
      { name: 'Central Processing Unit (CPU)', description: 'Executes algorithms for signal analysis, feature extraction (e.g., QRS detection), and decision making.'},
      { name: 'Memory', description: 'Stores raw data, processed data, and system software.'},
      { name: 'Real-Time Dissemination System', description: 'Manages the output of data, alarms, and visualizations to the user or other systems.'},
    ],
    signalPath: [
      { stage: 'Sensing', description: 'Biopotential electrodes capture electrical activity from the heart.' },
      { stage: 'Preprocessing', description: 'Raw analog signal is amplified and filtered.' },
      { stage: 'Digitization', description: 'Conditioned analog signal is converted to digital samples by the ADC.' },
      { stage: 'Processing', description: 'CPU analyzes the digital ECG data for patterns, abnormalities, and vital metrics.' },
      { stage: 'Output', description: 'Processed information, waveforms, and alerts are displayed or transmitted.' },
    ],
  },
  {
    id: 'ecg_measurement_amplifier',
    title: 'Measurement/Instrumentation Amplifier',
    imageUrl: 'https://i.imgur.com/3wXfT7B.png', // Re-using diag5
    overview: 'The measurement amplifier, typically an instrumentation amplifier, is crucial for ECG systems. It amplifies the very small ECG bio-potential (millivolts range) while rejecting common-mode noise picked up by the body.',
    type: 'Schematic',
    keyAspects: [
      { name: 'Differential Input', description: 'Takes input from two electrodes (e.g., LA, RA) and amplifies the difference, rejecting common signals present on both inputs (noise).'},
      { name: 'High Common-Mode Rejection Ratio (CMRR)', description: 'Essential for rejecting 50/60Hz power line interference and other environmental noise.'},
      { name: 'High Input Impedance', description: 'Minimizes loading effects on the signal source (electrodes), ensuring accurate signal capture.'},
      { name: 'Gain Control', description: 'Allows adjustment of the amplification factor to suit different signal strengths or ADC input ranges. Often set by a single resistor in instrumentation amplifiers.'},
      { name: 'Output Stage', description: 'Provides a low-impedance output to drive subsequent stages like filters or an ADC.'},
    ],
    signalPath: [
      { stage: 'Input', description: 'Differential ECG signal from electrodes enters the amplifier.' },
      { stage: 'Amplification', description: 'The differential voltage is amplified by a precise gain factor.' },
      { stage: 'Common-Mode Rejection', description: 'Noise common to both inputs is significantly attenuated.' },
      { stage: 'Output', description: 'The amplified, cleaner signal is outputted for further processing.' },
    ],
  },
  {
    id: 'ecg_digital_filter',
    title: 'Digital Filter Signal Flow',
    imageUrl: 'https://i.imgur.com/uF2F20N.png', // Re-using diag4
    overview: 'Digital filters are used post-ADC to further refine the ECG signal by removing specific frequency components like baseline wander, powerline noise (if not fully removed by analog filters), and muscle artifacts.',
    type: 'Block Diagram',
    keyAspects: [
      { name: 'Analog-to-Digital Conversion (A/D)', description: 'The analog ECG signal must first be converted to a digital sequence before digital filtering can be applied.'},
      { name: 'Digital Filter Algorithm', description: 'Mathematical operations (e.g., FIR or IIR filter equations) are applied to the digital samples. Different algorithms target different types of noise.'},
      { name: 'Filter Coefficients', description: 'These numerical values define the characteristics of the filter (e.g., cutoff frequencies, passband, stopband).'},
      { name: 'Processed Digital Signal', description: 'The output of the filter is a modified sequence of digital samples, ideally with noise reduced and important ECG features preserved.'},
      { name: 'Digital-to-Analog Conversion (D/A)', description: 'Shown for completeness if an analog output is needed, but often the processed digital signal is used directly for analysis or display.'},
    ],
    signalPath: [
      { stage: 'Input (Digital)', description: 'Digitized ECG samples enter the filter.' },
      { stage: 'Processing', description: 'The filter algorithm manipulates sample values based on its coefficients.' },
      { stage: 'Output (Digital)', description: 'Filtered digital ECG samples are produced. These can be used for further digital analysis or converted back to analog if needed.' },
    ],
  },
  {
    id: 'ecg_drl_circuit',
    title: 'Driven Right Leg (DRL) Circuit',
    imageUrl: 'https://i.imgur.com/DslR6hb.png', // Re-using diag6
    overview: 'The Driven Right Leg (DRL) circuit is an active noise cancellation technique used in biopotential amplifiers (like ECG) to reduce common-mode interference. It senses the common-mode voltage on the patient and feeds back an inverted version to the patient\'s right leg, effectively cancelling it out.',
    type: 'Schematic',
    keyAspects: [
      { name: 'Common-Mode Sensing', description: 'The DRL circuit averages the signals from the primary measurement electrodes (e.g., LA and RA) to estimate the common-mode voltage.'},
      { name: 'Inverting Amplifier', description: 'This amplifier inverts the sensed common-mode voltage.'},
      { name: 'Feedback Loop', description: 'The inverted common-mode signal is applied to the patient via the Right Leg (RL) electrode.'},
      { name: 'Safety Resistor', description: 'A current-limiting resistor is always included in series with the RL electrode for patient safety.'},
    ],
    signalPath: [
      { stage: 'Sensing Common Mode', description: 'Voltages from main electrodes are summed/averaged.'},
      { stage: 'Inversion & Amplification', description: 'The common-mode signal is inverted and amplified by the DRL op-amp.'},
      { stage: 'Feedback', description: 'The output is fed to the Right Leg electrode, effectively driving the patient\'s body to a potential that minimizes common-mode voltage at the amplifier inputs.'},
    ],
  },
   {
    id: 'ecg_arduino_acquisition',
    title: 'Arduino-Based Data Acquisition',
    imageUrl: 'https://i.imgur.com/xL2cCoH.png', // Re-using diag3
    overview: 'This diagram outlines how an Arduino microcontroller can be used for ECG data acquisition. It highlights the path from sensors to the computer display.',
    type: 'Block Diagram',
    keyAspects: [
      { name: 'Sensors', description: 'ECG electrodes and potentially other physiological sensors.'},
      { name: 'Signal Conditioning', description: 'External circuitry (amplifiers, filters) to prepare the analog signal for the Arduino\'s ADC.'},
      { name: 'ADC (Analog-to-Digital Converter)', description: 'The Arduino\'s built-in ADC converts the conditioned analog ECG signal to digital values.'},
      { name: 'Arduino Microcontroller', description: 'Reads digital values from ADC, performs basic processing or timing, and transmits data to a computer.'},
      { name: 'Computer', description: 'Receives data from Arduino (e.g., via USB/Serial), performs advanced processing, visualization, and storage.'},
      { name: 'Display', description: 'Shows the ECG waveform and other relevant information on the computer screen.'},
    ],
    signalPath: [
      { stage: 'Sensing & Conditioning', description: 'ECG signal is captured and conditioned externally.'},
      { stage: 'Digitization (Arduino ADC)', description: 'Arduino converts the analog signal to digital samples.'},
      { stage: 'Data Transfer', description: 'Arduino sends digital data to the computer, typically over a serial connection.'},
      { stage: 'Processing & Visualization (Computer)', description: 'Computer processes the data, displays the waveform, and can store it.'},
    ],
  }
];


// Simple SVG Icons
export const IconHeart: React.FC<{ className?: string }> = ({ className }) => (
  React.createElement(
    "svg",
    {
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 24 24",
      fill: "currentColor",
      className: className || "w-6 h-6",
      "aria-hidden": "true",
    },
    React.createElement("path", {
      d: "M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z",
    })
  )
);

export const IconLungs: React.FC<{ className?: string }> = ({ className }) => (
 React.createElement(
    "svg",
    {
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 24 24",
      fill: "currentColor",
      className: className || "w-6 h-6",
      "aria-hidden": "true",
    },
    React.createElement("path", {
      fillRule: "evenodd",
      d: "M5.25 12a.75.75 0 01.75-.75h.01a.75.75 0 01.75.75v.01a.75.75 0 01-.75.75H6a.75.75 0 01-.75-.75v-.01zM6 13.5a.75.75 0 00-.75.75v.01c0 .414.336.75.75.75h.01a.75.75 0 00.75-.75V14.25a.75.75 0 00-.75-.75h-.01zM7.5 12a.75.75 0 01.75-.75h.01a.75.75 0 01.75.75v.01a.75.75 0 01-.75.75H8.25a.75.75 0 01-.75-.75v-.01zM8.25 13.5a.75.75 0 00-.75.75v.01c0 .414.336.75.75.75h.01a.75.75 0 00.75-.75V14.25a.75.75 0 00-.75-.75h-.01zM9.75 12a.75.75 0 01.75-.75h.01a.75.75 0 01.75.75v.01a.75.75 0 01-.75.75H10.5a.75.75 0 01-.75-.75v-.01zM10.5 13.5a.75.75 0 00-.75.75v.01c0 .414.336.75.75.75h.01a.75.75 0 00.75-.75V14.25a.75.75 0 00-.75-.75h-.01zm1.5-1.5a.75.75 0 01.75-.75h.01a.75.75 0 01.75.75v.01a.75.75 0 01-.75.75H12.75a.75.75 0 01-.75-.75v-.01zM12 13.5a.75.75 0 00-.75.75v.01c0 .414.336.75.75.75h.01a.75.75 0 00.75-.75V14.25a.75.75 0 00-.75-.75h-.01zM3.75 12a.75.75 0 01.75-.75H19.5a.75.75 0 01.75.75v6a.75.75 0 01-.75.75H4.5a.75.75 0 01-.75-.75v-6zM21 11.25A2.25 2.25 0 0018.75 9H15V7.5A2.25 2.25 0 0012.75 5.25h-1.5A2.25 2.25 0 009 7.5V9H5.25A2.25 2.25 0 003 11.25v7.5A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75v-7.5zM13.5 9V7.5c0-.414-.336-.75-.75-.75h-1.5c-.414 0-.75.336-.75.75V9h3z",
      clipRule: "evenodd",
    })
  )
);

export const IconThermometer: React.FC<{ className?: string }> = ({ className }) => (
  React.createElement(
    "svg",
    {
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 24 24",
      fill: "currentColor",
      className: className || "w-6 h-6",
      "aria-hidden": "true",
    },
    React.createElement("path", {
      d: "M12 12.75a2.25 2.25 0 100 4.5 2.25 2.25 0 000-4.5z",
    }),
    React.createElement("path", {
      fillRule: "evenodd",
      d: "M12 3.75a.75.75 0 01.75.75v6.19l-2.47-2.47a.75.75 0 010-1.06l.53-.53a.75.75 0 111.06 1.06l-1.22 1.22V4.5A.75.75 0 0112 3.75zM14.25 7.5A2.25 2.25 0 0012 5.25a2.25 2.25 0 00-2.25 2.25V12A2.25 2.25 0 0012 16.5a2.25 2.25 0 002.25-2.25V7.5z",
      clipRule: "evenodd",
    }),
    React.createElement("path", {
      fillRule: "evenodd",
      d: "M10.5 9.75a.75.75 0 00-1.5 0v4.5a.75.75 0 001.5 0v-4.5zM12 8.25a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0112 8.25zm1.5.75a.75.75 0 00-1.5 0v4.5a.75.75 0 001.5 0v-4.5z",
      clipRule: "evenodd",
    }),
    React.createElement("path", {
      fillRule: "evenodd",
      d: "M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25zM6.55 9.835A.75.75 0 017.2 9.2l.25.35a.75.75 0 01-.6 1.2l-.25-.35a.75.75 0 01-.6-1.065zm8.9 0a.75.75 0 01.6 1.065l-.25.35a.75.75 0 01-.6-1.2l.25-.35zm-4.45 4.415a.75.75 0 001.06 0l1.06-1.06a.75.75 0 00-1.06-1.06l-1.06 1.06a.75.75 0 000 1.06z",
      clipRule: "evenodd",
    })
  )
);

export const IconGauge: React.FC<{ className?: string }> = ({ className }) => (
  React.createElement(
    "svg",
    {
      xmlns: "http://www.w3.org/2000/svg",
      viewBox: "0 0 24 24",
      fill: "currentColor",
      className: className || "w-6 h-6",
      "aria-hidden": "true",
    },
    React.createElement("path", {
      fillRule: "evenodd",
      d: "M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25zm.045 3.99a.75.75 0 00-1.418-.243l-6.364 11.64a.75.75 0 00.607 1.113h12.728a.75.75 0 00.607-1.113l-6.364-11.64a.75.75 0 00-.816-.243zm-.39 4.135a.75.75 0 011.06 0l3 3a.75.75 0 11-1.06 1.06L12 10.811l-2.655 2.654a.75.75 0 11-1.06-1.06l3-3z",
      clipRule: "evenodd",
    })
  )
);

export const IconSaturation: React.FC<{ className?: string }> = ({ className }) => (
    React.createElement(
      "svg",
      {
        xmlns: "http://www.w3.org/2000/svg",
        viewBox: "0 0 24 24",
        fill: "currentColor",
        className: className || "w-6 h-6",
        "aria-hidden": "true",
      },
      React.createElement("path", {
        d: "M12 9.31l-2.24 2.24a3.014 3.014 0 000 4.24l2.24 2.24 2.24-2.24a3.014 3.014 0 000-4.24L12 9.31zM12 3C6.486 3 2 7.486 2 13c0 5.515 4.486 10 10 10s10-4.485 10-10c0-5.514-4.486-10-10-10zm0 18c-4.411 0-8-3.589-8-8s3.589-8 8-8 8 3.589 8 8-3.589 8-8 8z",
      }),
      React.createElement("path", {
        d: "M12 7a1 1 0 100-2 1 1 0 000 2zM16.293 5.293a1 1 0 10-1.414 1.414L16.586 12l-1.707 1.707a1 1 0 001.414 1.414L19.414 12l-3.121-3.121zM7.707 5.293L4.586 8.414 7.707 11.535a1 1 0 001.414-1.414L8.414 10l2.121-2.121a1 1 0 10-1.414-1.414L7.707 5.293z",
      })
    )
  );

export const INITIAL_ECG_DATA: { time: number; value: number }[] = Array(50)
  .fill(null)
  .map((_, i) => ({ time: i, value: 0 }));

// Placeholder for the original diagram if still needed independently
export const DIAGRAM_IMAGE_URL = "https://i.imgur.com/Yq7XH1M.png";
export const DIAGRAM_TITLE = "Remote Patient Monitoring System";
