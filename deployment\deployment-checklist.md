# Deployment Checklist

## Pre-Deployment
- [ ] Domain purchased and configured
- [ ] Hosting account set up
- [ ] SSL certificate ready
- [ ] Backup of existing site (if applicable)

## File Upload
- [ ] Upload all HTML files
- [ ] Upload assets folder with CSS/JS
- [ ] Upload configuration files (.htaccess, robots.txt, sitemap.xml)
- [ ] Set proper file permissions (644 for files, 755 for directories)

## Testing
- [ ] All pages load correctly
- [ ] Navigation works on desktop and mobile
- [ ] Forms function properly (when implemented)
- [ ] SSL certificate is active (https://)
- [ ] 404 page displays correctly
- [ ] Site loads in under 3 seconds

## SEO Setup
- [ ] Submit sitemap to Google Search Console
- [ ] Submit sitemap to Bing Webmaster Tools
- [ ] Verify meta tags are correct
- [ ] Check robots.txt is accessible

## Performance
- [ ] Run Google PageSpeed Insights test
- [ ] Check GTmetrix performance score
- [ ] Verify Core Web Vitals are in "Good" range
- [ ] Test on multiple devices and browsers

## Security
- [ ] HTTPS redirect working
- [ ] Security headers configured
- [ ] Sensitive files protected
- [ ] Regular backup schedule established

## Analytics (Next Phase)
- [ ] Google Analytics 4 installed
- [ ] Google Tag Manager configured
- [ ] Conversion tracking set up
- [ ] Email automation connected
