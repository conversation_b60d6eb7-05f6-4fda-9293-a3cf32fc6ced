# Visual Element Specifications for Biomedical Electronics Virtual Lab

## Hero Section Video Background Specifications

### Primary Hero Video: "Circuit Building in Action"
**Duration:** 90 seconds (looping)
**Resolution:** 1920x1080 (Full HD)
**Format:** MP4 (H.264), WebM (VP9) for web compatibility
**File Size:** <10MB for optimal loading

**Scene Breakdown:**
1. **Opening (0-15s):** Fade in from black to clean virtual breadboard interface
2. **Component Placement (15-30s):** User cursor drags op-amp IC onto breadboard with smooth animation
3. **Wiring Animation (30-50s):** Intelligent wire routing with color-coded connections
4. **Signal Injection (50-65s):** Function generator connects, noisy ECG signal appears on oscilloscope
5. **Filter Application (65-80s):** Filter circuit activates, signal becomes clean
6. **Final Result (80-90s):** Clean ECG waveform with heartbeat pattern, fade to loop

**Technical Requirements:**
- **Color Palette:** Primary blue (#2563eb), success green (#10b981), warning orange (#f59e0b)
- **Animation Style:** Smooth, professional, 60fps
- **UI Elements:** Realistic virtual instruments with accurate displays
- **Accessibility:** Include closed captions and audio description track

**Audio Specifications:**
- **Background Music:** Subtle, inspiring, technology-focused (120-130 BPM)
- **Sound Effects:** Realistic click sounds for component placement, oscilloscope beeps
- **Voiceover:** Optional 30-second version with professional narrator explaining process

---

## Screenshot Gallery Specifications

### Screenshot 1: Virtual Breadboard with Op-Amp Circuit
**Dimensions:** 1200x800px
**File Format:** PNG with transparency support
**Content Requirements:**
- **Circuit:** 3-op-amp instrumentation amplifier for ECG
- **Components Visible:** 
  - 3x LM741 op-amps with clear pin labels
  - Precision resistors (1%, color-coded)
  - Bypass capacitors
  - Power supply connections (+15V, -15V, GND)
- **Wiring:** Color-coded (red=power, black=ground, blue=signal, green=feedback)
- **Labels:** Component values clearly visible (10kΩ, 100kΩ, etc.)
- **Background:** Clean white breadboard with realistic tie-point shadows
- **Lighting:** Soft, even lighting with subtle component shadows

### Screenshot 2: Virtual Oscilloscope Display
**Dimensions:** 1200x800px
**Content Requirements:**
- **Waveforms:** 
  - Channel 1 (Yellow): Noisy input ECG signal with 60Hz interference
  - Channel 2 (Blue): Clean filtered output showing clear QRS complexes
- **Measurements:** 
  - Heart rate: 72 BPM
  - Peak-to-peak amplitude: 1.2V
  - Frequency analysis showing 60Hz notch
- **Controls:** Realistic oscilloscope interface with proper knobs and buttons
- **Grid:** Standard oscilloscope graticule with time/voltage markings
- **Cursors:** Active measurement cursors showing precise values

### Screenshot 3: Educator Dashboard
**Dimensions:** 1400x900px
**Content Requirements:**
- **Class Overview:** 
  - 24 students enrolled
  - Progress bars showing completion percentages
  - Color-coded status indicators (green=on track, yellow=behind, red=needs help)
- **Recent Activity:** Live feed of student submissions and achievements
- **Analytics Panel:** 
  - Average completion time per module
  - Common error patterns
  - Engagement metrics
- **Assignment Management:** List of active and upcoming assignments
- **Student Details:** Expandable cards with individual progress

### Screenshot 4: Fault Diagnosis Interface
**Dimensions:** 1200x800px
**Content Requirements:**
- **Circuit Display:** ECG amplifier with highlighted fault location (shorted capacitor)
- **Diagnostic Tools:** 
  - Virtual multimeter showing unexpected readings
  - Oscilloscope displaying distorted waveform
  - Component tester interface
- **Fault Indication:** 
  - Red highlight around faulty component
  - "Fault Identified!" success message
  - Diagnostic reasoning explanation
- **Scoring:** Points awarded for correct diagnosis and methodology

---

## Interactive Element Specifications

### Virtual Breadboard Component Library
**Interface Design:**
- **Layout:** Collapsible sidebar with categorized components
- **Categories:** 
  - Passive (R, L, C)
  - Active (Op-amps, transistors)
  - Digital (Logic gates, microcontrollers)
  - Biomedical (ECG electrodes, sensors)
  - Instruments (DMM, oscilloscope, function generator)

**Drag-and-Drop Behavior:**
- **Visual Feedback:** Component highlights on hover, shadow follows cursor during drag
- **Snap-to-Grid:** Automatic alignment to breadboard tie points
- **Collision Detection:** Prevents component overlap with visual warning
- **Connection Validation:** Smart suggestions for proper connections

### Virtual Instrument Interfaces

#### Digital Oscilloscope
**Screen Specifications:**
- **Display:** 1024x768 virtual screen with realistic phosphor glow effect
- **Channels:** 4 channels with individual color coding
- **Controls:** 
  - Rotary encoders with realistic click feedback
  - Push buttons with LED indicators
  - Touch-sensitive screen areas for modern scope features
- **Measurements:** Automatic parameter measurement with statistical analysis
- **Math Functions:** FFT, integration, differentiation with real-time updates

#### Function Generator
**Interface Elements:**
- **Waveform Selection:** Sine, square, triangle, sawtooth, noise, arbitrary
- **Parameter Controls:** Frequency (1mHz to 100MHz), amplitude, offset, duty cycle
- **Modulation:** AM, FM, PWM with visual representation
- **Output Display:** Real-time waveform preview with parameter overlay
- **Sync Options:** Internal/external triggering with phase control

---

## Animation Specifications

### Component Placement Animation
**Duration:** 0.5 seconds
**Easing:** Cubic-bezier(0.25, 0.46, 0.45, 0.94) for natural feel
**Sequence:**
1. Component scales from 0.8x to 1.0x during placement
2. Subtle bounce effect on contact with breadboard
3. Connection points highlight briefly to show available connections
4. Fade-in of component label and value

### Wire Routing Animation
**Duration:** 1.0 seconds
**Style:** Smooth bezier curve drawing from start to end point
**Visual Effects:**
- Current flow animation (moving dots along wire)
- Color change based on signal type (DC=red, AC=blue, digital=green)
- Thickness variation based on current magnitude
- Glow effect for high-frequency signals

### Fault Injection Animation
**Duration:** 2.0 seconds
**Sequence:**
1. Component begins normal operation (0-0.5s)
2. Gradual degradation with visual indicators (0.5-1.5s)
3. Complete failure with red highlight and warning icon (1.5-2.0s)
4. Oscilloscope waveform shows corresponding signal distortion

---

## Responsive Design Specifications

### Mobile Optimization (320px - 768px)
**Layout Adaptations:**
- **Navigation:** Hamburger menu with slide-out drawer
- **Hero Section:** Vertical layout with stacked elements
- **Feature Cards:** Single column grid with increased padding
- **Virtual Lab:** Touch-optimized controls with larger hit targets

**Touch Interactions:**
- **Component Placement:** Long press to select, drag to move
- **Zoom Controls:** Pinch-to-zoom for detailed circuit view
- **Instrument Controls:** Swipe gestures for parameter adjustment
- **Haptic Feedback:** Vibration on successful connections (where supported)

### Tablet Optimization (768px - 1024px)
**Layout Features:**
- **Split View:** Circuit on left, instruments on right
- **Floating Panels:** Draggable instrument windows
- **Gesture Support:** Two-finger rotation for component orientation
- **Stylus Support:** Precision drawing for custom circuit layouts

---

## Accessibility Specifications

### Visual Accessibility
**Color Contrast:** WCAG 2.1 AA compliance (4.5:1 minimum ratio)
**Color Blindness Support:**
- Alternative visual indicators (patterns, shapes, labels)
- Customizable color schemes for different types of color blindness
- High contrast mode with enhanced visibility

### Motor Accessibility
**Keyboard Navigation:**
- Full keyboard control for all interactive elements
- Logical tab order through interface
- Keyboard shortcuts for common actions
- Sticky keys support for complex key combinations

**Alternative Input Methods:**
- Voice control integration for hands-free operation
- Eye tracking support for cursor control
- Switch control compatibility for assistive devices
- Adjustable timing for interactions

### Cognitive Accessibility
**Interface Simplification:**
- Distraction-free mode with minimal UI
- Progressive disclosure of advanced features
- Clear visual hierarchy with consistent styling
- Contextual help and tooltips

**Learning Support:**
- Multiple representation modes (visual, auditory, kinesthetic)
- Adjustable pacing and difficulty levels
- Built-in glossary with audio pronunciations
- Step-by-step guided tutorials

---

## Performance Specifications

### Loading Optimization
**Initial Page Load:** <3 seconds on 3G connection
**Component Library:** Lazy loading with <500ms response time
**Circuit Simulation:** Real-time updates at 60fps
**Image Optimization:** 
- WebP format with JPEG fallback
- Responsive images with srcset
- Progressive loading for large images

### Browser Compatibility
**Supported Browsers:**
- Chrome 90+ (primary target)
- Firefox 88+
- Safari 14+
- Edge 90+
- Mobile Safari (iOS 14+)
- Chrome Mobile (Android 10+)

**Fallback Support:**
- Graceful degradation for older browsers
- Alternative interfaces for unsupported features
- Clear messaging about browser requirements

---

## Brand Guidelines for Visual Elements

### Color Palette
**Primary Colors:**
- Blue: #2563eb (primary actions, links)
- Dark Blue: #1d4ed8 (hover states)
- Light Blue: #dbeafe (backgrounds, highlights)

**Secondary Colors:**
- Green: #10b981 (success, positive feedback)
- Orange: #f59e0b (warnings, attention)
- Red: #ef4444 (errors, critical alerts)
- Gray: #6b7280 (text, neutral elements)

### Typography
**Primary Font:** Inter (Google Fonts)
- Headings: 700 weight
- Body text: 400 weight
- UI elements: 500 weight
- Code/technical: 'Fira Code' monospace

**Font Sizes:**
- H1: 3rem (48px) desktop, 2.5rem (40px) mobile
- H2: 2.5rem (40px) desktop, 2rem (32px) mobile
- H3: 2rem (32px) desktop, 1.5rem (24px) mobile
- Body: 1rem (16px) all devices
- Small: 0.875rem (14px) all devices

### Iconography
**Style:** Outline style with 2px stroke weight
**Library:** Font Awesome 6 (primary), custom SVGs for specialized icons
**Sizes:** 16px, 24px, 32px, 48px standard sizes
**Colors:** Inherit from parent element or use brand colors

### Spacing System
**Base Unit:** 0.25rem (4px)
**Scale:** 4px, 8px, 12px, 16px, 24px, 32px, 48px, 64px, 96px
**Component Padding:** 1rem (16px) minimum
**Section Spacing:** 4rem (64px) desktop, 3rem (48px) mobile

---

## Wireframe Specifications

### Landing Page Wireframe Structure
```
[Navigation Bar]
├── Logo + Brand Name
├── Menu Items (Features, Curriculum, Demo, Pricing, Contact)
└── CTA Button (Request Demo)

[Hero Section - Full Viewport Height]
├── Background Video/Animation
├── Headline (Center-aligned)
├── Subheading (Center-aligned)
├── Primary CTA Button
├── Secondary CTA Link
└── Scroll Indicator

[Features Section - 4-Column Grid]
├── Section Title
└── Feature Cards (Icon + Title + Description)

[Curriculum Timeline - Vertical Layout]
├── Section Title
└── Timeline Items (Number + Content + Skills)

[Personas Tabs - Horizontal Tabs]
├── Tab Navigation
└── Tab Content Panels

[Demo Section - Video + Gallery]
├── Main Video Player
└── Screenshot Gallery (4-item grid)

[Testimonials - 3-Column Grid]
├── Testimonial Cards (Quote + Author)

[FAQ - Accordion Layout]
├── Question/Answer Pairs

[Final CTA - Centered]
├── Headline + Description
└── Button Pair

[Footer - Multi-column]
├── Brand Info
├── Contact Details
├── Quick Links
└── Legal Links
```

### Virtual Lab Interface Wireframe
```
[Top Navigation]
├── Lab Logo
├── Module Navigation
├── Progress Indicator
├── User Menu
└── Help/Support

[Main Content Area - Split Layout]
├── Left Panel (60%)
│   ├── Virtual Breadboard
│   ├── Component Library (Collapsible)
│   └── Circuit Controls
└── Right Panel (40%)
    ├── Virtual Instruments (Tabbed)
    ├── Measurement Display
    └── Notes/Instructions

[Bottom Panel - Collapsible]
├── Fault Simulation Controls
├── Assessment Questions
└── Progress Tracking

[Floating Elements]
├── Zoom Controls
├── Undo/Redo Buttons
├── Save/Load Circuit
└── Help Tooltips
```

---

## Technical Implementation Requirements

### Video Production Specifications
**Equipment Requirements:**
- **Camera:** 4K capable for future-proofing
- **Screen Recording:** 60fps minimum for smooth animations
- **Audio:** Professional microphone for voiceovers
- **Lighting:** Soft, even lighting for screen recordings

**Post-Production:**
- **Software:** Adobe After Effects for animations, Premiere Pro for editing
- **Compression:** H.264 for compatibility, VP9 for efficiency
- **Optimization:** Multiple bitrates for adaptive streaming
- **Captions:** SRT format with accurate timing

### Image Asset Creation
**Software Requirements:**
- **Vector Graphics:** Adobe Illustrator or Figma for scalable icons
- **Raster Images:** Photoshop for photo editing and mockups
- **Screenshots:** High-resolution captures with consistent styling
- **Optimization:** ImageOptim or similar for web optimization

**File Organization:**
```
/assets
├── /images
│   ├── /screenshots (1200x800, 2400x1600 retina)
│   ├── /icons (SVG + PNG fallbacks)
│   ├── /backgrounds (WebP + JPEG)
│   └── /mockups (PSD source files)
├── /videos
│   ├── /hero (MP4, WebM, poster images)
│   ├── /demos (segmented for faster loading)
│   └── /tutorials (chapter-based organization)
└── /animations
    ├── /lottie (JSON format for web)
    ├── /gif (fallback animations)
    └── /css (pure CSS animations)
```

### Interactive Element Development
**Framework Requirements:**
- **Frontend:** React.js with TypeScript for type safety
- **Animation:** Framer Motion for smooth transitions
- **3D Graphics:** Three.js for advanced visualizations
- **State Management:** Redux for complex interactions

**Component Architecture:**
```
/components
├── /VirtualLab
│   ├── Breadboard.tsx
│   ├── ComponentLibrary.tsx
│   ├── WireManager.tsx
│   └── CircuitSimulator.tsx
├── /Instruments
│   ├── Oscilloscope.tsx
│   ├── FunctionGenerator.tsx
│   ├── Multimeter.tsx
│   └── SpectrumAnalyzer.tsx
├── /UI
│   ├── Navigation.tsx
│   ├── ProgressTracker.tsx
│   ├── HelpSystem.tsx
│   └── AccessibilityControls.tsx
└── /Animations
    ├── ComponentPlacement.tsx
    ├── WireRouting.tsx
    ├── SignalFlow.tsx
    └── FaultInjection.tsx
```

---

## Quality Assurance Specifications

### Visual Testing Requirements
**Cross-Browser Testing:**
- Automated screenshot comparison across browsers
- Visual regression testing with tools like Percy or Chromatic
- Manual testing on physical devices
- Accessibility auditing with axe-core

**Performance Testing:**
- Lighthouse audits for Core Web Vitals
- WebPageTest for detailed performance analysis
- Real User Monitoring (RUM) implementation
- A/B testing for conversion optimization

### User Experience Testing
**Usability Testing Protocol:**
1. **Task-Based Testing:** Users complete specific learning objectives
2. **Think-Aloud Protocol:** Verbal feedback during interaction
3. **Eye Tracking:** Understanding visual attention patterns
4. **Accessibility Testing:** Screen reader and keyboard navigation
5. **Mobile Testing:** Touch interaction and responsive behavior

**Success Metrics:**
- **Task Completion Rate:** >90% for core learning objectives
- **Time to Competency:** <2 hours for basic circuit building
- **Error Recovery:** <30 seconds to resolve common mistakes
- **Satisfaction Score:** >4.5/5 on post-session surveys

### Content Quality Standards
**Educational Content Review:**
- Subject matter expert validation
- Pedagogical effectiveness assessment
- Accuracy verification for all technical content
- Regular updates for industry standards compliance

**Visual Content Standards:**
- Brand consistency across all materials
- Accessibility compliance (WCAG 2.1 AA)
- Cultural sensitivity and inclusivity
- Professional quality and polish
