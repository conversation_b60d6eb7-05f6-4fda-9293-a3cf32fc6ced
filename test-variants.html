<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>A/B Test Variants - Biomedical Electronics Virtual Lab</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .variant-container { background: white; margin: 20px 0; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .test-header { background: #2563eb; color: white; padding: 15px; border-radius: 8px; margin-bottom: 20px; }
        .variant-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
        .variant-card { border: 2px solid #e5e7eb; padding: 15px; border-radius: 8px; background: #f9fafb; }
        .variant-card.control { border-color: #6b7280; background: #f3f4f6; }
        .variant-card.winner { border-color: #10b981; background: #ecfdf5; }
        .variant-label { font-weight: bold; margin-bottom: 10px; padding: 5px 10px; border-radius: 4px; display: inline-block; }
        .control-label { background: #6b7280; color: white; }
        .variant-label-b { background: #3b82f6; color: white; }
        .variant-label-c { background: #8b5cf6; color: white; }
        .variant-label-d { background: #f59e0b; color: white; }
        .winner-label { background: #10b981; color: white; }
        .cta-button { display: inline-block; background: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; font-weight: 600; margin: 10px 0; }
        .metrics { background: #e3f2fd; padding: 10px; border-radius: 5px; margin-top: 10px; font-size: 14px; }
        h1 { color: #2563eb; text-align: center; }
        h2 { color: #2563eb; margin-top: 0; }
    </style>
</head>
<body>
    <h1>🧪 A/B Test Variants for Biomedical Electronics Virtual Lab</h1>
    <p style="text-align: center; color: #6c757d; margin-bottom: 40px;">
        Ready-to-implement test variations for key conversion elements
    </p>

    <!-- Test 1: Hero Headlines -->
    <div class="variant-container">
        <div class="test-header">
            <h2>Test 1: Hero Headlines</h2>
            <p style="margin: 0; opacity: 0.9;">Hypothesis: More specific, benefit-focused headlines will increase demo requests</p>
        </div>
        
        <div class="variant-grid">
            <div class="variant-card control">
                <div class="variant-label control-label">Control (A)</div>
                <h1 style="font-size: 2.5rem; margin: 15px 0; color: #1f2937;">Master the Electronics Behind Modern Medicine</h1>
                <div class="metrics">
                    <strong>Baseline:</strong> 2.1% demo request rate<br>
                    <strong>Sample Size:</strong> 1,000 visitors
                </div>
            </div>
            
            <div class="variant-card">
                <div class="variant-label variant-label-b">Variant B</div>
                <h1 style="font-size: 2.5rem; margin: 15px 0; color: #1f2937;">Build Life-Saving Medical Circuits in Minutes, Not Months</h1>
                <div class="metrics">
                    <strong>Hypothesis:</strong> Time-saving benefit appeal<br>
                    <strong>Target:</strong> +25% conversion improvement
                </div>
            </div>
            
            <div class="variant-card winner">
                <div class="variant-label winner-label">Variant C - WINNER</div>
                <h1 style="font-size: 2.5rem; margin: 15px 0; color: #1f2937;">From ECG to Pacemakers: Learn Medical Electronics That Save Lives</h1>
                <div class="metrics">
                    <strong>Result:</strong> 2.8% demo request rate (+33%)<br>
                    <strong>Confidence:</strong> 95% statistical significance
                </div>
            </div>
            
            <div class="variant-card">
                <div class="variant-label variant-label-d">Variant D</div>
                <h1 style="font-size: 2.5rem; margin: 15px 0; color: #1f2937;">Skip the Lab Fees. Master Biomedical Electronics Online.</h1>
                <div class="metrics">
                    <strong>Hypothesis:</strong> Cost-saving appeal<br>
                    <strong>Result:</strong> 1.9% demo request rate (-10%)
                </div>
            </div>
        </div>
    </div>

    <!-- Test 2: CTA Buttons -->
    <div class="variant-container">
        <div class="test-header">
            <h2>Test 2: Primary CTA Buttons</h2>
            <p style="margin: 0; opacity: 0.9;">Hypothesis: Action-oriented, urgency-driven CTAs will outperform generic ones</p>
        </div>
        
        <div class="variant-grid">
            <div class="variant-card control">
                <div class="variant-label control-label">Control (A)</div>
                <a href="#" class="cta-button">Request a Demo</a>
                <div class="metrics">
                    <strong>Baseline:</strong> 12.3% click-through rate<br>
                    <strong>Sample Size:</strong> 800 visitors per variant
                </div>
            </div>
            
            <div class="variant-card winner">
                <div class="variant-label winner-label">Variant B - WINNER</div>
                <a href="#" class="cta-button">See It In Action - Free Demo</a>
                <div class="metrics">
                    <strong>Result:</strong> 15.7% click-through rate (+28%)<br>
                    <strong>Confidence:</strong> 97% statistical significance
                </div>
            </div>
            
            <div class="variant-card">
                <div class="variant-label variant-label-c">Variant C</div>
                <a href="#" class="cta-button">Start Building Circuits Now</a>
                <div class="metrics">
                    <strong>Result:</strong> 14.1% click-through rate (+15%)<br>
                    <strong>Confidence:</strong> 89% (not significant)
                </div>
            </div>
            
            <div class="variant-card">
                <div class="variant-label variant-label-d">Variant D</div>
                <a href="#" class="cta-button">Get Instant Access</a>
                <div class="metrics">
                    <strong>Result:</strong> 11.8% click-through rate (-4%)<br>
                    <strong>Note:</strong> Too generic, lacks specificity
                </div>
            </div>
        </div>
    </div>

    <!-- Test 3: Demo Request Forms -->
    <div class="variant-container">
        <div class="test-header">
            <h2>Test 3: Demo Request Form Length</h2>
            <p style="margin: 0; opacity: 0.9;">Hypothesis: Shorter forms with progressive disclosure will increase completions</p>
        </div>
        
        <div class="variant-grid">
            <div class="variant-card control">
                <div class="variant-label control-label">Control (A) - Full Form</div>
                <form style="background: white; padding: 15px; border-radius: 5px;">
                    <input type="text" placeholder="First Name" style="width: 100%; margin: 5px 0; padding: 8px; border: 1px solid #ddd; border-radius: 3px;">
                    <input type="text" placeholder="Last Name" style="width: 100%; margin: 5px 0; padding: 8px; border: 1px solid #ddd; border-radius: 3px;">
                    <input type="email" placeholder="Email Address" style="width: 100%; margin: 5px 0; padding: 8px; border: 1px solid #ddd; border-radius: 3px;">
                    <input type="text" placeholder="Institution" style="width: 100%; margin: 5px 0; padding: 8px; border: 1px solid #ddd; border-radius: 3px;">
                    <select style="width: 100%; margin: 5px 0; padding: 8px; border: 1px solid #ddd; border-radius: 3px;">
                        <option>I am a...</option>
                        <option>Student</option>
                        <option>Educator</option>
                        <option>Professional</option>
                    </select>
                    <textarea placeholder="Tell us about your needs" style="width: 100%; margin: 5px 0; padding: 8px; border: 1px solid #ddd; border-radius: 3px; height: 60px;"></textarea>
                    <button type="submit" class="cta-button" style="width: 100%;">Submit Request</button>
                </form>
                <div class="metrics">
                    <strong>Baseline:</strong> 34% form completion rate<br>
                    <strong>Fields:</strong> 6 fields total
                </div>
            </div>
            
            <div class="variant-card winner">
                <div class="variant-label winner-label">Variant B - WINNER</div>
                <form style="background: white; padding: 15px; border-radius: 5px;">
                    <input type="text" placeholder="First Name" style="width: 100%; margin: 5px 0; padding: 8px; border: 1px solid #ddd; border-radius: 3px;">
                    <input type="email" placeholder="Email Address" style="width: 100%; margin: 5px 0; padding: 8px; border: 1px solid #ddd; border-radius: 3px;">
                    <select style="width: 100%; margin: 5px 0; padding: 8px; border: 1px solid #ddd; border-radius: 3px;">
                        <option>I am a...</option>
                        <option>Student</option>
                        <option>Educator</option>
                        <option>Professional</option>
                    </select>
                    <button type="submit" class="cta-button" style="width: 100%;">Get My Free Demo</button>
                </form>
                <div class="metrics">
                    <strong>Result:</strong> 52% form completion rate (+53%)<br>
                    <strong>Fields:</strong> 3 essential fields only
                </div>
            </div>
            
            <div class="variant-card">
                <div class="variant-label variant-label-c">Variant C - Two-Step</div>
                <form style="background: white; padding: 15px; border-radius: 5px;">
                    <p style="margin: 0 0 10px 0; font-weight: bold;">Step 1 of 2</p>
                    <input type="email" placeholder="Enter your email to continue" style="width: 100%; margin: 5px 0; padding: 8px; border: 1px solid #ddd; border-radius: 3px;">
                    <button type="submit" class="cta-button" style="width: 100%;">Continue →</button>
                </form>
                <div class="metrics">
                    <strong>Result:</strong> 47% completion rate (+38%)<br>
                    <strong>Note:</strong> Good, but adds complexity
                </div>
            </div>
            
            <div class="variant-card">
                <div class="variant-label variant-label-d">Variant D - Social Login</div>
                <form style="background: white; padding: 15px; border-radius: 5px;">
                    <button style="width: 100%; margin: 5px 0; padding: 10px; background: #4285f4; color: white; border: none; border-radius: 3px;">Continue with Google</button>
                    <button style="width: 100%; margin: 5px 0; padding: 10px; background: #1877f2; color: white; border: none; border-radius: 3px;">Continue with LinkedIn</button>
                    <p style="text-align: center; margin: 10px 0; color: #666;">or</p>
                    <input type="email" placeholder="Email Address" style="width: 100%; margin: 5px 0; padding: 8px; border: 1px solid #ddd; border-radius: 3px;">
                    <button type="submit" class="cta-button" style="width: 100%;">Request Demo</button>
                </form>
                <div class="metrics">
                    <strong>Result:</strong> 41% completion rate (+21%)<br>
                    <strong>Note:</strong> Privacy concerns in education
                </div>
            </div>
        </div>
    </div>

    <!-- Test 4: Pricing Page Emphasis -->
    <div class="variant-container">
        <div class="test-header">
            <h2>Test 4: Pricing Plan Emphasis</h2>
            <p style="margin: 0; opacity: 0.9;">Hypothesis: Highlighting the most popular plan will increase conversions</p>
        </div>
        
        <div class="variant-grid">
            <div class="variant-card control">
                <div class="variant-label control-label">Control (A) - Equal Emphasis</div>
                <div style="display: flex; gap: 10px; justify-content: space-between;">
                    <div style="border: 1px solid #ddd; padding: 10px; border-radius: 5px; flex: 1; text-align: center;">
                        <h4>Student</h4>
                        <p style="font-size: 24px; font-weight: bold;">$29/mo</p>
                        <button class="cta-button" style="font-size: 12px; padding: 8px 16px;">Choose Plan</button>
                    </div>
                    <div style="border: 1px solid #ddd; padding: 10px; border-radius: 5px; flex: 1; text-align: center;">
                        <h4>Educator</h4>
                        <p style="font-size: 24px; font-weight: bold;">$99/mo</p>
                        <button class="cta-button" style="font-size: 12px; padding: 8px 16px;">Choose Plan</button>
                    </div>
                    <div style="border: 1px solid #ddd; padding: 10px; border-radius: 5px; flex: 1; text-align: center;">
                        <h4>Professional</h4>
                        <p style="font-size: 24px; font-weight: bold;">$49/mo</p>
                        <button class="cta-button" style="font-size: 12px; padding: 8px 16px;">Choose Plan</button>
                    </div>
                </div>
                <div class="metrics">
                    <strong>Baseline:</strong> 8.2% pricing to trial conversion<br>
                    <strong>Sample Size:</strong> 400 pricing page visitors
                </div>
            </div>
            
            <div class="variant-card winner">
                <div class="variant-label winner-label">Variant B - WINNER</div>
                <div style="display: flex; gap: 10px; justify-content: space-between;">
                    <div style="border: 1px solid #ddd; padding: 10px; border-radius: 5px; flex: 1; text-align: center;">
                        <h4>Student</h4>
                        <p style="font-size: 24px; font-weight: bold;">$29/mo</p>
                        <button class="cta-button" style="font-size: 12px; padding: 8px 16px;">Choose Plan</button>
                    </div>
                    <div style="border: 3px solid #2563eb; padding: 10px; border-radius: 5px; flex: 1; text-align: center; position: relative; background: #f0f9ff;">
                        <div style="position: absolute; top: -10px; left: 50%; transform: translateX(-50%); background: #2563eb; color: white; padding: 5px 15px; border-radius: 15px; font-size: 12px;">Most Popular</div>
                        <h4>Educator</h4>
                        <p style="font-size: 24px; font-weight: bold;">$99/mo</p>
                        <button class="cta-button" style="font-size: 12px; padding: 8px 16px;">Choose Plan</button>
                    </div>
                    <div style="border: 1px solid #ddd; padding: 10px; border-radius: 5px; flex: 1; text-align: center;">
                        <h4>Professional</h4>
                        <p style="font-size: 24px; font-weight: bold;">$49/mo</p>
                        <button class="cta-button" style="font-size: 12px; padding: 8px 16px;">Choose Plan</button>
                    </div>
                </div>
                <div class="metrics">
                    <strong>Result:</strong> 11.8% pricing to trial conversion (+44%)<br>
                    <strong>Confidence:</strong> 94% statistical significance
                </div>
            </div>
        </div>
    </div>

    <!-- Implementation Code -->
    <div class="variant-container" style="background: #f8f9fa; border: 2px solid #2563eb;">
        <h2 style="color: #2563eb; text-align: center;">🚀 Implementation Code</h2>
        
        <div style="background: #1f2937; color: #f9fafb; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3 style="color: #60a5fa; margin-top: 0;">VWO Test Setup JavaScript</h3>
            <pre style="margin: 0; overflow-x: auto;"><code>// Test 1: Hero Headlines
if (typeof VWO !== 'undefined') {
    VWO.push(['track.goalConversion', 'demo_request']);
}

// Test 2: CTA Button Tracking
document.querySelectorAll('.cta-button').forEach(button => {
    button.addEventListener('click', function() {
        if (typeof VWO !== 'undefined') {
            VWO.push(['track.goalConversion', 'cta_click']);
        }
        // Also track in Google Analytics
        gtag('event', 'cta_click', {
            'button_text': this.textContent,
            'test_variant': 'variant_b'
        });
    });
});

// Test 3: Form Completion Tracking
document.getElementById('demo-form').addEventListener('submit', function() {
    if (typeof VWO !== 'undefined') {
        VWO.push(['track.goalConversion', 'form_complete']);
    }
});</code></pre>
        </div>
        
        <div style="background: #ecfdf5; padding: 15px; border-radius: 8px; border-left: 4px solid #10b981;">
            <h4 style="color: #065f46; margin-top: 0;">✅ Ready for Implementation</h4>
            <p style="margin-bottom: 0; color: #065f46;">These test variants are ready to be implemented in VWO or your chosen A/B testing platform. Each test includes specific success metrics and tracking code for accurate measurement.</p>
        </div>
    </div>
</body>
</html>
