<!DOCTYPE html>
<html>
<head>
    <title>Biomedical Electronics Virtual Lab</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f0f8ff;
        }
        .header {
            background-color: #2563eb;
            color: white;
            padding: 20px;
            text-align: center;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        .hero {
            background: linear-gradient(45deg, #2563eb, #7c3aed);
            color: white;
            padding: 60px 20px;
            text-align: center;
            margin: 20px 0;
            border-radius: 10px;
        }
        .hero h1 {
            font-size: 2.5em;
            margin-bottom: 20px;
        }
        .hero p {
            font-size: 1.2em;
            margin-bottom: 30px;
        }
        .button {
            background-color: #ffffff;
            color: #2563eb;
            padding: 15px 30px;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
            display: inline-block;
            margin: 10px;
        }
        .button:hover {
            background-color: #f0f0f0;
        }
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 40px 0;
        }
        .feature {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        .feature h3 {
            color: #2563eb;
            margin-bottom: 15px;
        }
        .nav {
            background-color: white;
            padding: 15px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .nav a {
            color: #2563eb;
            text-decoration: none;
            margin: 0 20px;
            font-weight: bold;
        }
        .nav a:hover {
            text-decoration: underline;
        }
        .contact {
            background-color: #2563eb;
            color: white;
            padding: 40px;
            text-align: center;
            border-radius: 10px;
            margin: 40px 0;
        }
        .footer {
            background-color: #1f2937;
            color: white;
            padding: 30px;
            text-align: center;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <div class="nav">
        <div class="container">
            <a href="index-simple.html">🏠 Home</a>
            <a href="curriculum-simple.html">📚 Curriculum</a>
            <a href="pricing-simple.html">💰 Pricing</a>
            <a href="#contact">📞 Contact</a>
        </div>
    </div>

    <!-- Header -->
    <div class="header">
        <h1>🔬 Biomedical Electronics Virtual Lab</h1>
        <p>Master the Electronics Behind Modern Medicine</p>
    </div>

    <!-- Main Content -->
    <div class="container">
        <!-- Hero Section -->
        <div class="hero">
            <h1>Transform Your Learning Experience</h1>
            <p>Build, test, and troubleshoot the essential circuits that power life-saving medical devices. Our fully interactive virtual lab bridges the gap between theory and real-world application.</p>
            <a href="mailto:<EMAIL>?subject=Demo Request" class="button">Request a Demo</a>
            <a href="curriculum-simple.html" class="button">View Curriculum</a>
        </div>

        <!-- Features Section -->
        <h2 style="text-align: center; color: #2563eb; font-size: 2em; margin: 40px 0;">Why Our Virtual Lab?</h2>
        
        <div class="features">
            <div class="feature">
                <h3>🔧 Interactive Circuit Building</h3>
                <p>Drag and drop components to build complex biomedical circuits. From basic op-amps to complete ECG systems, experience hands-on learning without physical constraints.</p>
            </div>
            
            <div class="feature">
                <h3>📊 Real-Time Signal Analysis</h3>
                <p>Watch signals flow through your circuits with our advanced oscilloscope and spectrum analyzer. Understand how each component affects the final output.</p>
            </div>
            
            <div class="feature">
                <h3>🛠️ Fault Simulation & Troubleshooting</h3>
                <p>Practice diagnosing real-world problems with our intelligent fault injection system. Build the critical thinking skills essential for medical device engineering.</p>
            </div>
            
            <div class="feature">
                <h3>🎓 Structured Learning Path</h3>
                <p>Progress through 10 comprehensive modules, from DC fundamentals to advanced biomedical applications. Each lesson builds practical skills you'll use in your career.</p>
            </div>
            
            <div class="feature">
                <h3>👥 Educator Dashboard</h3>
                <p>Track student progress, assign custom exercises, and monitor learning outcomes. Perfect for classroom integration and remote learning environments.</p>
            </div>
            
            <div class="feature">
                <h3>📱 Anywhere, Anytime Access</h3>
                <p>Learn on any device, anywhere. Our cloud-based platform ensures your progress is always saved and accessible across all your devices.</p>
            </div>
        </div>

        <!-- Curriculum Preview -->
        <div style="background: white; padding: 40px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); margin: 40px 0;">
            <h2 style="color: #2563eb; text-align: center;">Complete Curriculum</h2>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-top: 30px;">
                <div style="border-left: 4px solid #2563eb; padding-left: 20px;">
                    <h4>Module 1: DC & AC Fundamentals</h4>
                    <p>Foundation of all biomedical electronics</p>
                </div>
                <div style="border-left: 4px solid #2563eb; padding-left: 20px;">
                    <h4>Module 4: Operational Amplifiers</h4>
                    <p>The workhorse of biomedical instrumentation</p>
                </div>
                <div style="border-left: 4px solid #2563eb; padding-left: 20px;">
                    <h4>Module 7: ECG Signal Processing</h4>
                    <p>Complete cardiac monitoring systems</p>
                </div>
                <div style="border-left: 4px solid #2563eb; padding-left: 20px;">
                    <h4>Module 10: Medical Device Safety</h4>
                    <p>Patient protection and regulatory compliance</p>
                </div>
            </div>
            <div style="text-align: center; margin-top: 30px;">
                <a href="curriculum-simple.html" class="button">View Complete Curriculum</a>
            </div>
        </div>

        <!-- Pricing Preview -->
        <div style="background: white; padding: 40px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); margin: 40px 0;">
            <h2 style="color: #2563eb; text-align: center;">Flexible Pricing Plans</h2>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-top: 30px;">
                <div style="text-align: center; padding: 20px; border: 2px solid #e5e7eb; border-radius: 10px;">
                    <h4>Student</h4>
                    <p style="font-size: 2em; color: #2563eb; font-weight: bold;">$29/mo</p>
                    <p>Perfect for individual learners</p>
                </div>
                <div style="text-align: center; padding: 20px; border: 3px solid #2563eb; border-radius: 10px; background: #f0f9ff;">
                    <div style="background: #2563eb; color: white; padding: 5px 15px; border-radius: 15px; font-size: 0.8em; margin-bottom: 10px;">Most Popular</div>
                    <h4>Educator</h4>
                    <p style="font-size: 2em; color: #2563eb; font-weight: bold;">$99/mo</p>
                    <p>Ideal for instructors and classes</p>
                </div>
                <div style="text-align: center; padding: 20px; border: 2px solid #e5e7eb; border-radius: 10px;">
                    <h4>Professional</h4>
                    <p style="font-size: 2em; color: #2563eb; font-weight: bold;">$49/mo</p>
                    <p>For working engineers</p>
                </div>
                <div style="text-align: center; padding: 20px; border: 2px solid #e5e7eb; border-radius: 10px;">
                    <h4>Institution</h4>
                    <p style="font-size: 2em; color: #2563eb; font-weight: bold;">Custom</p>
                    <p>Universities & organizations</p>
                </div>
            </div>
            <div style="text-align: center; margin-top: 30px;">
                <a href="pricing-simple.html" class="button">View All Plans</a>
            </div>
        </div>
    </div>

    <!-- Contact Section -->
    <div id="contact" class="contact">
        <h2>Ready to Transform Your Learning?</h2>
        <p>Join thousands of students and educators who are mastering biomedical electronics with our virtual lab.</p>
        <a href="mailto:<EMAIL>?subject=Demo Request" class="button">Request Your Free Demo</a>
        <a href="mailto:<EMAIL>?subject=General Inquiry" class="button">Contact Us</a>
    </div>

    <!-- Footer -->
    <div class="footer">
        <h3>Biomedical Electronics Virtual Lab</h3>
        <p>Developed by Dr. Mohammed Yagoub Esmail, SUST - BME</p>
        <p>📧 Email: <EMAIL></p>
        <p>📞 Phone: +249 912 867 327 | +966 538 076 790</p>
        <p>&copy; 2025. All Rights Reserved.</p>
    </div>

    <script>
        // Simple smooth scrolling
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({ behavior: 'smooth' });
                }
            });
        });
        
        console.log('✅ Simple website loaded successfully!');
        alert('✅ Website is working! You should see the Biomedical Electronics Virtual Lab homepage.');
    </script>
</body>
</html>
