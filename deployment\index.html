<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Master biomedical electronics with our interactive virtual lab. Build, test, and troubleshoot ECG circuits, op-amps, and medical device electronics safely.">
    <meta name="keywords" content="biomedical electronics, virtual lab, ECG circuits, medical device training, op-amp simulation, biomedical engineering education">
    <title>Biomedical Electronics Virtual Lab - Master Medical Device Electronics</title>
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/styles.css">

    <!-- Google Tag Manager -->
    <script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
    new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
    j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
    'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
    })(window,document,'script','dataLayer','GTM-XXXXXXX');</script>
    <!-- End Google Tag Manager -->
</head>
<body>
    <!-- Google Tag Manager (noscript) -->
    <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-XXXXXXX"
    height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
    <!-- End Google Tag Manager (noscript) -->
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <i class="fas fa-heartbeat"></i>
                <span>BioMed Virtual Lab</span>
            </div>
            <ul class="nav-menu">
                <li><a href="#features" class="nav-link">Features</a></li>
                <li><a href="#curriculum" class="nav-link">Curriculum</a></li>
                <li><a href="#demo" class="nav-link">Demo</a></li>
                <li><a href="#pricing" class="nav-link">Pricing</a></li>
                <li><a href="#contact" class="nav-link">Contact</a></li>
                <li><a href="#demo" class="nav-link cta-button">Request Demo</a></li>
            </ul>
            <div class="hamburger">
                <span class="bar"></span>
                <span class="bar"></span>
                <span class="bar"></span>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero">
        <div class="hero-background">
            <div class="hero-video-placeholder">
                <!-- Placeholder for dynamic video background -->
                <div class="video-simulation">
                    <div class="virtual-breadboard"></div>
                    <div class="oscilloscope-display"></div>
                    <div class="component-drag"></div>
                </div>
            </div>
        </div>
        <div class="hero-content">
            <div class="container">
                <h1 class="hero-title">Master the Electronics Behind Modern Medicine</h1>
                <p class="hero-subtitle">Build, test, and troubleshoot the essential circuits that power life-saving medical devices. Our fully interactive virtual lab bridges the gap between theory and real-world application.</p>
                <div class="hero-buttons">
                    <a href="#demo" class="btn btn-primary">Request a Demo</a>
                    <a href="#curriculum" class="btn btn-secondary">View Curriculum ↓</a>
                </div>
            </div>
        </div>
        <div class="hero-scroll-indicator">
            <i class="fas fa-chevron-down"></i>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="features">
        <div class="container">
            <h2 class="section-title">Why Our Virtual Lab?</h2>
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-microchip"></i>
                    </div>
                    <h3>Intuitive Virtual Workbench</h3>
                    <p>Go from concept to circuit in minutes. Our drag-and-drop interface, realistic breadboard, and smart wiring system let you build and experiment with the same logic you'd use in a physical lab.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-tools"></i>
                    </div>
                    <h3>Comprehensive Instrument Suite</h3>
                    <p>Everything you need is at your fingertips. Access a vast library of components—from basic resistors to specialized ECG electrodes—and use professional-grade virtual instruments.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-bug"></i>
                    </div>
                    <h3>Realistic Fault Simulation</h3>
                    <p>Develop elite troubleshooting skills. Our unique Fault Insertion engine challenges you to diagnose and repair realistic circuit problems, preparing you for critical medical device maintenance.</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <h3>Integrated Educator Dashboard</h3>
                    <p>Teach, track, and assess with ease. Assign labs, monitor student progress in real-time, and review submissions, all within a single, powerful platform.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Curriculum Section -->
    <section id="curriculum" class="curriculum">
        <div class="container">
            <h2 class="section-title">Your Learning Journey: From Fundamentals to System Design</h2>
            <div class="curriculum-timeline">
                <div class="timeline-item">
                    <div class="timeline-marker">1</div>
                    <div class="timeline-content">
                        <h3>DC & AC Circuit Fundamentals</h3>
                        <p class="core-concept"><strong>Core Concept:</strong> Master the foundational laws of electronics.</p>
                        <ul class="skills-list">
                            <li>Verify Ohm's & Kirchhoff's Laws</li>
                            <li>Analyze series/parallel circuits</li>
                            <li>Understand AC waveform characteristics</li>
                        </ul>
                    </div>
                </div>
                <div class="timeline-item">
                    <div class="timeline-marker">4</div>
                    <div class="timeline-content">
                        <h3>Operational Amplifiers</h3>
                        <p class="core-concept"><strong>Core Concept:</strong> Harness the workhorse of bio-instrumentation.</p>
                        <ul class="skills-list">
                            <li>Build inverting, non-inverting, and difference amplifiers</li>
                            <li>Amplify tiny bio-potentials while rejecting noise</li>
                            <li>Design ECG front-end amplifiers</li>
                        </ul>
                    </div>
                </div>
                <div class="timeline-item">
                    <div class="timeline-marker">5</div>
                    <div class="timeline-content">
                        <h3>Active Filters for Bio-Signal Processing</h3>
                        <p class="core-concept"><strong>Core Concept:</strong> Isolate the signal from the noise.</p>
                        <ul class="skills-list">
                            <li>Design low-pass, high-pass, and notch filters</li>
                            <li>Remove baseline wander and power-line interference</li>
                            <li>Process biological signals effectively</li>
                        </ul>
                    </div>
                </div>
                <div class="timeline-item">
                    <div class="timeline-marker">6</div>
                    <div class="timeline-content">
                        <h3>Biomedical Sensor Interfacing</h3>
                        <p class="core-concept"><strong>Core Concept:</strong> Convert physical phenomena into electrical signals.</p>
                        <ul class="skills-list">
                            <li>Build interface circuits for thermistors and strain gauges</li>
                            <li>Master instrumentation amplifiers for ECG acquisition</li>
                            <li>Design photodetector circuits for PPG</li>
                        </ul>
                    </div>
                </div>
                <div class="timeline-item">
                    <div class="timeline-marker">8</div>
                    <div class="timeline-content">
                        <h3>System Integration & Design</h3>
                        <p class="core-concept"><strong>Core Concept:</strong> Combine circuit blocks to create functional instruments.</p>
                        <ul class="skills-list">
                            <li>Design complete ECG front-end systems</li>
                            <li>Build pulse oximeter analog stages</li>
                            <li>Apply knowledge in capstone projects</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Personas Section -->
    <section class="personas">
        <div class="container">
            <h2 class="section-title">Designed for Every Stage of Your Career</h2>
            <div class="personas-tabs">
                <div class="tab-buttons">
                    <button class="tab-button active" data-tab="student">BME Student</button>
                    <button class="tab-button" data-tab="bmet">BMET Student</button>
                    <button class="tab-button" data-tab="professional">Professional</button>
                    <button class="tab-button" data-tab="educator">Educator</button>
                </div>
                <div class="tab-content">
                    <div class="tab-panel active" id="student">
                        <h3>For the BME Student</h3>
                        <p>Visualize complex circuit behavior and test your hypotheses in a risk-free environment. Go beyond the textbook to build the circuits you're studying, solidifying your understanding and building a project portfolio that stands out.</p>
                    </div>
                    <div class="tab-panel" id="bmet">
                        <h3>For the BMET Student</h3>
                        <p>Develop the practical, hands-on skills you need for a successful career. Practice assembling, testing, and troubleshooting common medical device circuits until it becomes second nature. Walk into any lab with confidence.</p>
                    </div>
                    <div class="tab-panel" id="professional">
                        <h3>For the Professional Engineer/Technician</h3>
                        <p>Quickly prototype new ideas, refresh your knowledge of core principles, and safely experiment with circuit designs. Enhance your problem-solving abilities and stay current with the technology that defines your field.</p>
                    </div>
                    <div class="tab-panel" id="educator">
                        <h3>For the Educator</h3>
                        <p>Transform your classroom. Assign interactive labs, effectively guide student learning with our structured modules, and easily assess comprehension through the integrated dashboard. Identify where students struggle and provide targeted support.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Demo Section -->
    <section id="demo" class="demo">
        <div class="container">
            <h2 class="section-title">Don't Just Read About It. See It.</h2>
            <div class="demo-video">
                <div class="video-placeholder">
                    <i class="fas fa-play-circle"></i>
                    <p>Watch how a student builds a complete ECG amplifier from scratch</p>
                </div>
            </div>
            <div class="demo-gallery">
                <div class="gallery-item">
                    <div class="placeholder-image">
                        <i class="fas fa-microchip"></i>
                    </div>
                    <p>Virtual breadboard showing complex op-amp circuit with color-coded wiring</p>
                </div>
                <div class="gallery-item">
                    <div class="placeholder-image">
                        <i class="fas fa-wave-square"></i>
                    </div>
                    <p>Oscilloscope displaying input and output waveforms with measurement cursors</p>
                </div>
                <div class="gallery-item">
                    <div class="placeholder-image">
                        <i class="fas fa-chart-bar"></i>
                    </div>
                    <p>Educator Dashboard showing class progress for assigned experiments</p>
                </div>
                <div class="gallery-item">
                    <div class="placeholder-image">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <p>"Fault Identified!" - Student successfully diagnosed a shorted capacitor</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Testimonials Section -->
    <section class="testimonials">
        <div class="container">
            <h2 class="section-title">Praise from Our Users</h2>
            <div class="testimonials-grid">
                <div class="testimonial-card">
                    <div class="testimonial-content">
                        <p>"The best tool I've found for teaching bio-instrumentation. The pre-built modules and the educator dashboard save me hours of prep time, and the troubleshooting scenarios are invaluable for teaching critical thinking."</p>
                    </div>
                    <div class="testimonial-author">
                        <div class="author-avatar">
                            <i class="fas fa-user-graduate"></i>
                        </div>
                        <div class="author-info">
                            <h4>Dr. Emily Carter</h4>
                            <p>Professor of Engineering</p>
                        </div>
                    </div>
                </div>
                <div class="testimonial-card">
                    <div class="testimonial-content">
                        <p>"This lab was a game-changer. I finally understood how an instrumentation amplifier works in an ECG circuit, something I struggled with in lectures. Building it myself made all the difference."</p>
                    </div>
                    <div class="testimonial-author">
                        <div class="author-avatar">
                            <i class="fas fa-user"></i>
                        </div>
                        <div class="author-info">
                            <h4>Alex R.</h4>
                            <p>B.S. Biomedical Engineering Student</p>
                        </div>
                    </div>
                </div>
                <div class="testimonial-card">
                    <div class="testimonial-content">
                        <p>"I use the lab to practice my diagnostic skills. Being able to work through fault scenarios on complex circuits is incredible training. It's much more effective than just reading schematics."</p>
                    </div>
                    <div class="testimonial-author">
                        <div class="author-avatar">
                            <i class="fas fa-user-tie"></i>
                        </div>
                        <div class="author-info">
                            <h4>Maria G.</h4>
                            <p>Certified BMET</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- FAQ Section -->
    <section class="faq">
        <div class="container">
            <h2 class="section-title">Frequently Asked Questions</h2>
            <div class="faq-accordion">
                <div class="faq-item">
                    <button class="faq-question">
                        <span>Do I need prior electronics experience?</span>
                        <i class="fas fa-chevron-down"></i>
                    </button>
                    <div class="faq-answer">
                        <p>No! The curriculum is designed to guide you from the absolute fundamentals of DC circuits all the way to advanced system design.</p>
                    </div>
                </div>
                <div class="faq-item">
                    <button class="faq-question">
                        <span>What makes this different from a generic circuit simulator?</span>
                        <i class="fas fa-chevron-down"></i>
                    </button>
                    <div class="faq-answer">
                        <p>Three things: Biomedical Context (every experiment is tied to a real medical application), Structured Curriculum (guided modules designed for learning), and Integrated Tools (like fault simulation and an educator dashboard) built specifically for biomedical education and training.</p>
                    </div>
                </div>
                <div class="faq-item">
                    <button class="faq-question">
                        <span>Can I design my own circuits?</span>
                        <i class="fas fa-chevron-down"></i>
                    </button>
                    <div class="faq-answer">
                        <p>Absolutely. While our guided modules provide a structured path, the platform is also a powerful sandbox. You can use the full component library to build and test your own custom designs.</p>
                    </div>
                </div>
                <div class="faq-item">
                    <button class="faq-question">
                        <span>Is this a standalone platform?</span>
                        <i class="fas fa-chevron-down"></i>
                    </button>
                    <div class="faq-answer">
                        <p>Yes. All features, including the educator dashboard and student tracking, are built-in. No third-party software or APIs are required.</p>
                    </div>
                </div>
                <div class="faq-item">
                    <button class="faq-question">
                        <span>Who developed this platform?</span>
                        <i class="fas fa-chevron-down"></i>
                    </button>
                    <div class="faq-answer">
                        <p>This virtual lab is the result of dedicated research and development in engineering education, led by Dr. Mohammed Yagoub Esmail of the Biomedical Engineering department at SUST.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Final CTA Section -->
    <section class="final-cta">
        <div class="container">
            <div class="cta-content">
                <h2>Ready to Revolutionize Your Learning?</h2>
                <p>Take the first step towards mastering biomedical electronics. See how our virtual lab can empower your students, your team, or your own career.</p>
                <div class="cta-buttons">
                    <a href="#contact" class="btn btn-primary">Request a Personalized Demo</a>
                    <a href="#pricing" class="btn btn-secondary">View Pricing & Plans</a>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>Biomedical Electronics Virtual Lab</h3>
                    <p>Developed by Dr. Mohammed Yagoub Esmail, SUST - BME</p>
                    <p>&copy; 2025. All Rights Reserved.</p>
                </div>
                <div class="footer-section">
                    <h4>Contact Information</h4>
                    <div class="contact-info">
                        <p><i class="fas fa-envelope"></i> <EMAIL></p>
                        <p><i class="fas fa-phone"></i> +249 912 867 327</p>
                        <p><i class="fas fa-phone"></i> +966 538 076 790</p>
                    </div>
                </div>
                <div class="footer-section">
                    <h4>Quick Links</h4>
                    <ul class="footer-links">
                        <li><a href="#features">Features</a></li>
                        <li><a href="#curriculum">Curriculum</a></li>
                        <li><a href="#demo">Demo</a></li>
                        <li><a href="#pricing">Pricing</a></li>
                        <li><a href="#contact">Contact</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Legal</h4>
                    <ul class="footer-links">
                        <li><a href="#privacy">Privacy Policy</a></li>
                        <li><a href="#terms">Terms of Service</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </footer>

    <!-- Analytics Tracking -->
    <script src="assets/js/analytics-tracking.js"></script>

    <!-- Main Site JavaScript -->
    <script src="assets/js/script.js"></script>

    <!-- Initialize Analytics with Event Tracking -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Track demo request buttons
            document.querySelectorAll('a[href*="demo"], .cta-button').forEach(button => {
                button.addEventListener('click', function(e) {
                    const buttonText = this.textContent.trim();
                    if (buttonText.toLowerCase().includes('demo')) {
                        AnalyticsTracker.trackDemoRequest('unknown', 'landing_page');
                    }
                });
            });

            // Track pricing page visits
            document.querySelectorAll('a[href*="pricing"]').forEach(link => {
                link.addEventListener('click', function() {
                    AnalyticsTracker.trackPricingInteraction('unknown', 'view_pricing');
                });
            });

            // Track curriculum page visits
            document.querySelectorAll('a[href*="curriculum"]').forEach(link => {
                link.addEventListener('click', function() {
                    AnalyticsTracker.trackCurriculumDownload(0, 'curriculum_overview');
                });
            });

            // Track persona tab interactions
            document.querySelectorAll('.tab-button').forEach(button => {
                button.addEventListener('click', function() {
                    const userType = this.getAttribute('data-tab');
                    AnalyticsTracker.setUserProperties(userType);
                });
            });
        });
    </script>
</body>
</html>
