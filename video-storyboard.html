<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hero Video Storyboard - Biomedical Electronics Virtual Lab</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .storyboard-container { background: white; margin: 20px 0; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .scene-header { background: #2563eb; color: white; padding: 15px; border-radius: 8px; margin-bottom: 20px; }
        .scene-grid { display: grid; grid-template-columns: 1fr 2fr; gap: 20px; margin-bottom: 30px; }
        .scene-visual { background: #f8f9fa; border: 2px dashed #dee2e6; padding: 40px; text-align: center; border-radius: 8px; min-height: 200px; display: flex; align-items: center; justify-content: center; }
        .scene-details { background: #f8f9fa; padding: 20px; border-radius: 8px; }
        .timeline { background: #e3f2fd; padding: 10px 15px; border-radius: 5px; margin: 10px 0; font-weight: bold; color: #1565c0; }
        .action-list { background: #f0f9ff; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .technical-notes { background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0; border-left: 4px solid #ffc107; }
        h1 { color: #2563eb; text-align: center; margin-bottom: 30px; }
        h2 { color: #2563eb; margin-top: 0; }
        h3 { color: #1f2937; margin-top: 0; }
        .mockup-placeholder { color: #6c757d; font-style: italic; font-size: 18px; }
        @media (max-width: 768px) {
            .scene-grid { grid-template-columns: 1fr; }
        }
    </style>
</head>
<body>
    <h1>🎬 Hero Video Storyboard: "Building Biomedical Circuits in the Virtual Lab"</h1>
    <p style="text-align: center; color: #6c757d; font-size: 18px; margin-bottom: 40px;">
        90-second demonstration of virtual circuit building and signal processing
    </p>

    <!-- Scene 1 -->
    <div class="storyboard-container">
        <div class="scene-header">
            <h2>Scene 1: Opening & Interface Reveal</h2>
            <div class="timeline">⏱️ Timeline: 0-15 seconds</div>
        </div>
        
        <div class="scene-grid">
            <div class="scene-visual">
                <div class="mockup-placeholder">
                    📱 Virtual Lab Interface<br>
                    Clean breadboard workspace<br>
                    Component library sidebar<br>
                    Professional instruments panel
                </div>
            </div>
            
            <div class="scene-details">
                <h3>Visual Elements</h3>
                <div class="action-list">
                    <strong>Key Actions:</strong>
                    <ul>
                        <li>Smooth fade-in from black (2 seconds)</li>
                        <li>Camera pan revealing full interface</li>
                        <li>Subtle glow on interactive elements</li>
                        <li>Grid lines and connection points visible</li>
                    </ul>
                </div>
                
                <div class="technical-notes">
                    <strong>Technical Notes:</strong>
                    <ul>
                        <li>Resolution: 1920x1080, 60fps</li>
                        <li>Clean, modern UI with biomedical branding</li>
                        <li>Organized component library (left)</li>
                        <li>Virtual instruments panel (right)</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Scene 2 -->
    <div class="storyboard-container">
        <div class="scene-header">
            <h2>Scene 2: Component Selection & Placement</h2>
            <div class="timeline">⏱️ Timeline: 15-30 seconds</div>
        </div>
        
        <div class="scene-grid">
            <div class="scene-visual">
                <div class="mockup-placeholder">
                    🔧 Op-Amp IC (LM741)<br>
                    Cursor hover → highlight<br>
                    Smooth drag animation<br>
                    Snap to breadboard position
                </div>
            </div>
            
            <div class="scene-details">
                <h3>Component Interaction</h3>
                <div class="action-list">
                    <strong>Animation Sequence:</strong>
                    <ul>
                        <li>Cursor hovers over LM741 op-amp (0.5s)</li>
                        <li>Component highlights with tooltip info</li>
                        <li>Smooth bezier curve drag path</li>
                        <li>Breadboard connection points light up</li>
                        <li>Satisfying "snap" animation on placement</li>
                    </ul>
                </div>
                
                <div class="technical-notes">
                    <strong>Visual Details:</strong>
                    <ul>
                        <li>Realistic component with pin labels</li>
                        <li>Subtle shadow and depth effects</li>
                        <li>Component specifications visible</li>
                        <li>Natural cursor movement patterns</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Scene 3 -->
    <div class="storyboard-container">
        <div class="scene-header">
            <h2>Scene 3: Intelligent Wire Routing</h2>
            <div class="timeline">⏱️ Timeline: 30-50 seconds</div>
        </div>
        
        <div class="scene-grid">
            <div class="scene-visual">
                <div class="mockup-placeholder">
                    🔌 Color-coded Wiring<br>
                    Red: +15V Power<br>
                    Black: Ground<br>
                    Blue: -15V Power<br>
                    Green: Signal paths
                </div>
            </div>
            
            <div class="scene-details">
                <h3>Circuit Construction</h3>
                <div class="action-list">
                    <strong>Wiring Sequence:</strong>
                    <ul>
                        <li>Power connections (+15V, -15V, GND)</li>
                        <li>Signal input routing</li>
                        <li>Feedback resistor placement</li>
                        <li>Gain-setting components</li>
                        <li>Current flow animation (moving dots)</li>
                    </ul>
                </div>
                
                <div class="technical-notes">
                    <strong>Animation Effects:</strong>
                    <ul>
                        <li>Wires draw smoothly point-to-point</li>
                        <li>Automatic obstacle avoidance</li>
                        <li>Connection validation indicators</li>
                        <li>Real-time circuit analysis</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Scene 4 -->
    <div class="storyboard-container">
        <div class="scene-header">
            <h2>Scene 4: Signal Injection & Noise Display</h2>
            <div class="timeline">⏱️ Timeline: 50-65 seconds</div>
        </div>
        
        <div class="scene-grid">
            <div class="scene-visual">
                <div class="mockup-placeholder">
                    📊 Oscilloscope Display<br>
                    Noisy ECG waveform<br>
                    60Hz interference<br>
                    Baseline wander<br>
                    Heart rate: 72 BPM
                </div>
            </div>
            
            <div class="scene-details">
                <h3>Signal Characteristics</h3>
                <div class="action-list">
                    <strong>Signal Elements:</strong>
                    <ul>
                        <li>Function generator window opens</li>
                        <li>ECG waveform selected from library</li>
                        <li>Signal cable connects to input</li>
                        <li>Realistic noise and interference</li>
                        <li>Professional oscilloscope display</li>
                    </ul>
                </div>
                
                <div class="technical-notes">
                    <strong>Visual Effects:</strong>
                    <ul>
                        <li>Realistic phosphor glow effect</li>
                        <li>Proper time base and scaling</li>
                        <li>Measurement cursors active</li>
                        <li>Parameter displays updating</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Scene 5 -->
    <div class="storyboard-container">
        <div class="scene-header">
            <h2>Scene 5: Filter Activation & Signal Cleaning</h2>
            <div class="timeline">⏱️ Timeline: 65-80 seconds</div>
        </div>
        
        <div class="scene-grid">
            <div class="scene-visual">
                <div class="mockup-placeholder">
                    🎛️ Active Filtering<br>
                    Filter components highlight<br>
                    Frequency response display<br>
                    Progressive noise reduction<br>
                    Clean signal emerges
                </div>
            </div>
            
            <div class="scene-details">
                <h3>Filter Processing</h3>
                <div class="action-list">
                    <strong>Filter Effects:</strong>
                    <ul>
                        <li>High-pass: removes baseline wander</li>
                        <li>Low-pass: eliminates high-freq noise</li>
                        <li>Notch filter: removes 60Hz interference</li>
                        <li>Real-time frequency analysis</li>
                        <li>Before/after signal comparison</li>
                    </ul>
                </div>
                
                <div class="technical-notes">
                    <strong>Visualization:</strong>
                    <ul>
                        <li>Spectrum analyzer display</li>
                        <li>Progressive cleaning animation</li>
                        <li>Filter response curves</li>
                        <li>Performance metrics updating</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Scene 6 -->
    <div class="storyboard-container">
        <div class="scene-header">
            <h2>Scene 6: Final Result & Loop Transition</h2>
            <div class="timeline">⏱️ Timeline: 80-90 seconds</div>
        </div>
        
        <div class="scene-grid">
            <div class="scene-visual">
                <div class="mockup-placeholder">
                    ✅ Perfect ECG Signal<br>
                    Clear P, QRS, T waves<br>
                    Heart rate: 72 BPM<br>
                    Signal quality: Excellent<br>
                    Noise level: <1%
                </div>
            </div>
            
            <div class="scene-details">
                <h3>Success Metrics</h3>
                <div class="action-list">
                    <strong>Final Display:</strong>
                    <ul>
                        <li>Clean ECG with perfect morphology</li>
                        <li>Heart rate measurement: 72 BPM</li>
                        <li>Signal quality indicator: Excellent</li>
                        <li>Filter effectiveness: 99.2%</li>
                        <li>Circuit performance metrics</li>
                    </ul>
                </div>
                
                <div class="technical-notes">
                    <strong>Loop Transition:</strong>
                    <ul>
                        <li>2-second hold on final result</li>
                        <li>Gentle fade to black (1 second)</li>
                        <li>Seamless loop back to beginning</li>
                        <li>Continuous background music</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Production Notes -->
    <div class="storyboard-container" style="background: #f8f9fa; border: 2px solid #2563eb;">
        <h2 style="color: #2563eb; text-align: center;">🎯 Production Notes</h2>
        
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
            <div>
                <h3>Technical Specifications</h3>
                <ul>
                    <li><strong>Resolution:</strong> 1920x1080 (Full HD)</li>
                    <li><strong>Frame Rate:</strong> 60fps</li>
                    <li><strong>Duration:</strong> 90 seconds (looping)</li>
                    <li><strong>Format:</strong> MP4 (H.264) + WebM (VP9)</li>
                    <li><strong>File Size:</strong> <10MB for web</li>
                </ul>
            </div>
            
            <div>
                <h3>Audio Requirements</h3>
                <ul>
                    <li><strong>Music:</strong> Ambient electronic, 120-130 BPM</li>
                    <li><strong>SFX:</strong> UI clicks, connection sounds</li>
                    <li><strong>Volume:</strong> -12dB to -18dB background</li>
                    <li><strong>Voiceover:</strong> Optional 30-second version</li>
                    <li><strong>Format:</strong> 48kHz, 16-bit stereo</li>
                </ul>
            </div>
            
            <div>
                <h3>Color Palette</h3>
                <ul>
                    <li><strong>Primary:</strong> #2563eb (UI elements)</li>
                    <li><strong>Success:</strong> #10b981 (connections)</li>
                    <li><strong>Warning:</strong> #f59e0b (measurements)</li>
                    <li><strong>Background:</strong> #f8fafc (clean)</li>
                    <li><strong>Text:</strong> #1f2937 (readable)</li>
                </ul>
            </div>
        </div>
        
        <div style="background: #e3f2fd; padding: 20px; border-radius: 8px; margin-top: 20px; text-align: center;">
            <h3 style="color: #1565c0; margin-top: 0;">🚀 Ready for Production</h3>
            <p style="margin-bottom: 0; color: #1565c0;">This storyboard provides complete guidance for creating a professional hero video that showcases the virtual lab's capabilities and engages visitors immediately upon landing on your website.</p>
        </div>
    </div>
</body>
</html>
