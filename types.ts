
export interface VitalSigns {
  heartRate: number;
  spo2: number;
  systolicBP: number;
  diastolicBP: number;
  respiratoryRate: number;
  temperature: number;
}

export interface ECGPoint {
  time: number; // Represents time or sequence
  value: number; // Represents ECG amplitude
}

export interface GeminiVitalSignsResponse extends VitalSigns {
  ecgDescription: string;
  ecgWaveform: ECGPoint[];
}

// Represents the structure of a grounding chunk from Gemini Search grounding
export interface GroundingChunkWeb {
  uri: string;
  title: string;
}
export interface GroundingChunk {
  web?: GroundingChunkWeb;
  // Other types of chunks can be added here if needed
}

export interface GroundingMetadata {
  groundingChunks?: GroundingChunk[];
  // Other grounding metadata fields can be added here
}

// Extends Gemini's GenerateContentResponse structure slightly for typing
// if we were to use it directly. For now, we parse its .text
// For actual GoogleGenAI.GenerateContentResponse, refer to @google/genai types.
// This is a simplified representation if we expect specific candidate structure.
export interface CustomGenerateContentResponse {
  text: string; // Assuming text is always present.
  candidates?: Array<{
    groundingMetadata?: GroundingMetadata;
    // Other candidate properties
  }>;
  // Other response properties
}

export enum PatientScenario {
  NORMAL = "Normal Healthy Adult",
  TACHYCARDIA = "Tachycardia",
  BRADYCARDIA = "Bradycardia",
  ATRIAL_FIBRILLATION = "Atrial Fibrillation",
  FEVER = "Fever (High Temperature)",
  HYPOTHERMIA = "Hypothermia (Low Temperature)",
  HYPERTENSION = "Hypertension (High BP)",
  HYPOTENSION = "Hypotension (Low BP)",
}

export interface ECGSystemComponentDetail {
  id: string;
  title: string;
  imageUrl: string;
  overview: string;
  keyAspects: Array<{ name: string; description: string; highlightPoints?: string[] }>;
  signalPath?: Array<{ stage: string; description: string; details?: string[] }>;
  type: 'Block Diagram' | 'Schematic' | 'Conceptual';
}
