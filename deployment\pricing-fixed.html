<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pricing Plans - Biomedical Electronics Virtual Lab</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Navigation */
        .navbar {
            background: white;
            padding: 1rem 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .nav-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .nav-logo {
            display: flex;
            align-items: center;
            font-size: 1.5rem;
            font-weight: 700;
            color: #2563eb;
        }

        .nav-logo i {
            margin-right: 0.5rem;
        }

        .nav-menu {
            display: flex;
            list-style: none;
            gap: 2rem;
        }

        .nav-link {
            text-decoration: none;
            color: #374151;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .nav-link:hover {
            color: #2563eb;
        }

        .cta-button {
            background: #2563eb;
            color: white !important;
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
        }

        /* Header */
        .pricing-hero {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 4rem 0;
            text-align: center;
        }

        .pricing-hero h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
        }

        .pricing-hero p {
            font-size: 1.25rem;
            opacity: 0.9;
            max-width: 600px;
            margin: 0 auto;
        }

        /* Pricing Grid */
        .pricing-section {
            padding: 4rem 0;
        }

        .pricing-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 2rem;
            margin: 3rem 0;
        }

        .pricing-card {
            background: white;
            border-radius: 1rem;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            position: relative;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border: 2px solid transparent;
        }

        .pricing-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .pricing-card.featured {
            border: 3px solid #2563eb;
            transform: scale(1.05);
        }

        .pricing-card.featured::before {
            content: 'Most Popular';
            position: absolute;
            top: -15px;
            left: 50%;
            transform: translateX(-50%);
            background: #2563eb;
            color: white;
            padding: 0.5rem 1.5rem;
            border-radius: 2rem;
            font-size: 0.9rem;
            font-weight: 600;
        }

        .plan-name {
            font-size: 1.5rem;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 0.5rem;
        }

        .plan-description {
            color: #6b7280;
            margin-bottom: 2rem;
            font-size: 0.95rem;
        }

        .plan-price {
            font-size: 3rem;
            font-weight: 700;
            color: #2563eb;
            margin-bottom: 0.5rem;
        }

        .plan-price .currency {
            font-size: 1.5rem;
            vertical-align: top;
        }

        .plan-price .period {
            font-size: 1rem;
            color: #6b7280;
            font-weight: 400;
        }

        .plan-features {
            list-style: none;
            padding: 0;
            margin: 2rem 0;
        }

        .plan-features li {
            padding: 0.75rem 0;
            display: flex;
            align-items: center;
            border-bottom: 1px solid #f3f4f6;
        }

        .plan-features li:last-child {
            border-bottom: none;
        }

        .plan-features .check {
            color: #10b981;
            margin-right: 0.75rem;
            font-weight: bold;
        }

        .plan-features .cross {
            color: #ef4444;
            margin-right: 0.75rem;
        }

        .plan-button {
            width: 100%;
            padding: 1rem;
            border: none;
            border-radius: 0.5rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .plan-button.primary {
            background: #2563eb;
            color: white;
        }

        .plan-button.primary:hover {
            background: #1d4ed8;
            transform: translateY(-2px);
        }

        .plan-button.secondary {
            background: #f3f4f6;
            color: #374151;
            border: 2px solid #e5e7eb;
        }

        .plan-button.secondary:hover {
            background: #e5e7eb;
        }

        .savings-badge {
            background: #10b981;
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.8rem;
            font-weight: 600;
            margin-left: 1rem;
        }

        /* Enterprise CTA */
        .enterprise-cta {
            background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
            color: white;
            padding: 3rem;
            border-radius: 1rem;
            text-align: center;
            margin: 3rem 0;
        }

        .btn {
            display: inline-block;
            padding: 1rem 2rem;
            border-radius: 0.5rem;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            margin: 0.5rem;
        }

        .btn-primary {
            background: #2563eb;
            color: white;
        }

        .btn-primary:hover {
            background: #1d4ed8;
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: transparent;
            border: 2px solid white;
            color: white;
        }

        .btn-secondary:hover {
            background: white;
            color: #1f2937;
        }

        /* Footer */
        .footer {
            background: #1f2937;
            color: white;
            padding: 3rem 0 1rem;
            text-align: center;
        }

        .footer p {
            margin: 0.5rem 0;
            opacity: 0.8;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .nav-menu {
                display: none;
            }

            .pricing-hero h1 {
                font-size: 2rem;
            }

            .pricing-card.featured {
                transform: none;
            }

            .plan-price {
                font-size: 2.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container container">
            <div class="nav-logo">
                <i class="fas fa-heartbeat"></i>
                <span>BioMed Virtual Lab</span>
            </div>
            <ul class="nav-menu">
                <li><a href="index-fixed.html" class="nav-link">Home</a></li>
                <li><a href="curriculum-fixed.html" class="nav-link">Curriculum</a></li>
                <li><a href="#pricing" class="nav-link">Pricing</a></li>
                <li><a href="mailto:<EMAIL>?subject=Demo Request" class="nav-link cta-button">Request Demo</a></li>
            </ul>
        </div>
    </nav>

    <!-- Pricing Hero -->
    <section class="pricing-hero">
        <div class="container">
            <h1>Choose Your Learning Path</h1>
            <p>Flexible pricing options designed for students, educators, and professionals. Start your journey to mastering biomedical electronics today.</p>
        </div>
    </section>

    <!-- Pricing Plans -->
    <section id="pricing" class="pricing-section">
        <div class="container">
            <div class="pricing-grid">
                <!-- Student Plan -->
                <div class="pricing-card">
                    <div class="plan-name">Student</div>
                    <div class="plan-description">Perfect for individual learners and students</div>
                    <div class="plan-price">
                        <span class="currency">$</span>29<span class="period">/month</span>
                    </div>
                    <div style="text-align: center; margin-bottom: 1rem;">
                        <span style="color: #6b7280;">or $290/year</span>
                        <span class="savings-badge">Save 17%</span>
                    </div>
                    <ul class="plan-features">
                        <li><span class="check">✓</span> Access to all 10 modules</li>
                        <li><span class="check">✓</span> Unlimited circuit building</li>
                        <li><span class="check">✓</span> Basic fault simulation</li>
                        <li><span class="check">✓</span> Progress tracking</li>
                        <li><span class="check">✓</span> Community forum access</li>
                        <li><span class="check">✓</span> Mobile app access</li>
                        <li><span class="cross">✗</span> Educator dashboard</li>
                        <li><span class="cross">✗</span> Advanced analytics</li>
                    </ul>
                    <a href="mailto:<EMAIL>?subject=Student Plan Interest" class="plan-button secondary">Start Free Trial</a>
                </div>

                <!-- Educator Plan -->
                <div class="pricing-card featured">
                    <div class="plan-name">Educator</div>
                    <div class="plan-description">Ideal for instructors and small classes</div>
                    <div class="plan-price">
                        <span class="currency">$</span>99<span class="period">/month</span>
                    </div>
                    <div style="text-align: center; margin-bottom: 1rem;">
                        <span style="color: #6b7280;">or $990/year</span>
                        <span class="savings-badge">Save 17%</span>
                    </div>
                    <ul class="plan-features">
                        <li><span class="check">✓</span> Everything in Student plan</li>
                        <li><span class="check">✓</span> Up to 50 student accounts</li>
                        <li><span class="check">✓</span> Educator dashboard</li>
                        <li><span class="check">✓</span> Assignment creation tools</li>
                        <li><span class="check">✓</span> Grade book integration</li>
                        <li><span class="check">✓</span> Advanced fault scenarios</li>
                        <li><span class="check">✓</span> Student progress analytics</li>
                        <li><span class="check">✓</span> Priority email support</li>
                    </ul>
                    <a href="mailto:<EMAIL>?subject=Educator Plan Interest" class="plan-button primary">Start Free Trial</a>
                </div>

                <!-- Professional Plan -->
                <div class="pricing-card">
                    <div class="plan-name">Professional</div>
                    <div class="plan-description">For working engineers and technicians</div>
                    <div class="plan-price">
                        <span class="currency">$</span>49<span class="period">/month</span>
                    </div>
                    <div style="text-align: center; margin-bottom: 1rem;">
                        <span style="color: #6b7280;">or $490/year</span>
                        <span class="savings-badge">Save 17%</span>
                    </div>
                    <ul class="plan-features">
                        <li><span class="check">✓</span> Everything in Student plan</li>
                        <li><span class="check">✓</span> Advanced troubleshooting scenarios</li>
                        <li><span class="check">✓</span> Industry-specific modules</li>
                        <li><span class="check">✓</span> Certification tracking</li>
                        <li><span class="check">✓</span> CPE credit documentation</li>
                        <li><span class="check">✓</span> Professional community</li>
                        <li><span class="check">✓</span> Monthly expert webinars</li>
                        <li><span class="check">✓</span> Priority support</li>
                    </ul>
                    <a href="mailto:<EMAIL>?subject=Professional Plan Interest" class="plan-button secondary">Start Free Trial</a>
                </div>

                <!-- Institution Plan -->
                <div class="pricing-card">
                    <div class="plan-name">Institution</div>
                    <div class="plan-description">For universities and large organizations</div>
                    <div class="plan-price">
                        <span style="font-size: 2rem;">Custom</span>
                    </div>
                    <div style="text-align: center; margin-bottom: 1rem;">
                        <span style="color: #6b7280;">Contact for pricing</span>
                    </div>
                    <ul class="plan-features">
                        <li><span class="check">✓</span> Everything in Educator plan</li>
                        <li><span class="check">✓</span> Unlimited student accounts</li>
                        <li><span class="check">✓</span> Custom branding</li>
                        <li><span class="check">✓</span> Advanced analytics dashboard</li>
                        <li><span class="check">✓</span> API access</li>
                        <li><span class="check">✓</span> Custom content development</li>
                        <li><span class="check">✓</span> Dedicated support manager</li>
                        <li><span class="check">✓</span> On-site training</li>
                    </ul>
                    <a href="mailto:<EMAIL>?subject=Institution Plan Interest" class="plan-button primary">Contact Sales</a>
                </div>
            </div>

            <!-- Enterprise CTA -->
            <div class="enterprise-cta">
                <h3 style="margin-bottom: 1rem; font-size: 1.8rem;">Need a Custom Solution?</h3>
                <p style="margin-bottom: 2rem; opacity: 0.9;">
                    We work with institutions worldwide to create tailored solutions that meet specific educational and training needs. 
                    From custom curriculum development to white-label platforms, we've got you covered.
                </p>
                <div style="display: flex; gap: 1rem; justify-content: center; flex-wrap: wrap;">
                    <a href="mailto:<EMAIL>?subject=Custom Solution Consultation" class="btn btn-primary">Schedule Consultation</a>
                    <a href="mailto:<EMAIL>?subject=Enterprise Guide Request" class="btn btn-secondary">Request Enterprise Guide</a>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <h3>Biomedical Electronics Virtual Lab</h3>
            <p>Developed by Dr. Mohammed Yagoub Esmail, SUST - BME</p>
            <p>Email: <EMAIL></p>
            <p>Phone: +249 912 867 327 | +966 538 076 790</p>
            <p>&copy; 2025. All Rights Reserved.</p>
        </div>
    </footer>

    <script>
        // Smooth scrolling
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        console.log('✅ Pricing page loaded successfully!');
    </script>
</body>
</html>
